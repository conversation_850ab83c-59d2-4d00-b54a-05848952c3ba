<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Info Popover Test</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Outlined" rel="stylesheet">
    
    <!-- Custom CSS -->
    <style>
        /* Info icon styling */
        .info-icon {
            font-size: 16px;
            color: #FE7720;
            cursor: pointer;
            margin-left: 4px;
            vertical-align: middle;
            transition: color 0.2s ease;
        }

        .info-icon:hover {
            color: #FFB347;
        }

        /* Ensure proper spacing in different contexts */
        .info-icon:not(:first-child) {
            margin-left: 4px;
        }

        .info-icon:not(:last-child) {
            margin-right: 2px;
        }

        /* Popover content styling */
        .popover .popover-body {
            padding: 12px;
            max-width: 300px;
        }

        .popover .popover-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            font-weight: 600;
            padding: 10px 12px;
        }

        /* Ensure proper spacing for HTML content in popovers */
        .popover-content p {
            margin-bottom: 8px;
        }

        .popover-content p:last-child {
            margin-bottom: 0;
        }

        .popover-content strong, 
        .popover-content b {
            font-weight: 600;
        }

        .popover-content em, 
        .popover-content i {
            font-style: italic;
        }

        .popover-content br {
            line-height: 1.6;
        }
        
        .container {
            margin-top: 50px;
        }
        
        .test-section {
            margin-bottom: 40px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Info Popover System Test</h1>
        
        <div class="test-section">
            <h3>Test 1: Basic Popover (No Title)</h3>
            <p>This is a test of internal flights information <i class="info-icon material-icons-outlined" 
               data-bs-toggle="popover" 
               data-bs-trigger="hover focus" 
               data-bs-placement="top" 
               data-bs-html="true" 
               data-bs-content="<div class='popover-content'>We'll introduce you to our trusted in-country supplier, who can arrange these flights based on your trip dates. The cost is typically around $450 per person.</div>" 
               data-info-id="internal-flights">info</i> for your trip.</p>
        </div>
        
        <div class="test-section">
            <h3>Test 2: Popover with Title</h3>
            <p>Check visa requirements <i class="info-icon material-icons-outlined" 
               data-bs-toggle="popover" 
               data-bs-trigger="hover focus" 
               data-bs-placement="top" 
               data-bs-html="true" 
               data-bs-title="Visa Information" 
               data-bs-content="<div class='popover-content'>Visa requirements vary by nationality. Please check with your local embassy for the most up-to-date information.</div>" 
               data-info-id="visa-requirements">info</i> before traveling.</p>
        </div>
        
        <div class="test-section">
            <h3>Test 3: Popover with HTML Content</h3>
            <p>Weather conditions vary by season <i class="info-icon material-icons-outlined"
               data-bs-toggle="popover"
               data-bs-trigger="hover focus"
               data-bs-placement="top"
               data-bs-html="true"
               data-bs-title="Weather Conditions"
               data-bs-content="<div class='popover-content'><strong>Dry Season:</strong> May to October<br><strong>Wet Season:</strong> November to April<br><em>Pack accordingly!</em></div>"
               data-info-id="weather-conditions">info</i> throughout the year.</p>
        </div>

        <div class="test-section">
            <h3>Test 3b: Popover with Links and Bold Text</h3>
            <p>For more information <i class="info-icon material-icons-outlined"
               data-bs-toggle="popover"
               data-bs-trigger="hover focus"
               data-bs-placement="top"
               data-bs-html="true"
               data-bs-title="Contact Details"
               data-bs-content="<div class='popover-content'>Email us at <a href=&quot;mailto:<EMAIL>&quot;><EMAIL></a> or call <strong>+44 1234 567890</strong></div>"
               data-info-id="contact">info</i> anytime.</p>
        </div>
        
        <div class="test-section">
            <h3>Test 4: Multiple Popovers</h3>
            <p>This trip includes accommodation <i class="info-icon material-icons-outlined" 
               data-bs-toggle="popover" 
               data-bs-trigger="hover focus" 
               data-bs-placement="top" 
               data-bs-html="true" 
               data-bs-title="Accommodation Details" 
               data-bs-content="<div class='popover-content'>Twin-share mountain lodges with basic amenities. Single supplements available.</div>" 
               data-info-id="accommodation">info</i> and meals <i class="info-icon material-icons-outlined" 
               data-bs-toggle="popover" 
               data-bs-trigger="hover focus" 
               data-bs-placement="top" 
               data-bs-html="true" 
               data-bs-title="Meal Information" 
               data-bs-content="<div class='popover-content'>All meals included except lunch on day 3. Dietary requirements can be accommodated.</div>" 
               data-info-id="meals">info</i> but excludes flights <i class="info-icon material-icons-outlined" 
               data-bs-toggle="popover" 
               data-bs-trigger="hover focus" 
               data-bs-placement="top" 
               data-bs-html="true" 
               data-bs-content="<div class='popover-content'>Internal flights cost approximately $450 per person and must be booked separately.</div>" 
               data-info-id="flights">info</i>.</p>
        </div>
        
        <div class="test-section">
            <h3>Test 5: Placeholder Processing Test</h3>
            <p><strong>Original placeholder syntax:</strong></p>
            <code>{{info:test-placeholder|Test Title|This is test content with &lt;strong&gt;HTML&lt;/strong&gt; formatting.}}</code>
            
            <p><strong>Should render as:</strong></p>
            <p>Test content <i class="info-icon material-icons-outlined" 
               data-bs-toggle="popover" 
               data-bs-trigger="hover focus" 
               data-bs-placement="top" 
               data-bs-html="true" 
               data-bs-title="Test Title" 
               data-bs-content="<div class='popover-content'>This is test content with <strong>HTML</strong> formatting.</div>" 
               data-info-id="test-placeholder">info</i> here.</p>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.6/dist/umd/popper.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <script>
        // Initialize popovers
        var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'))
        var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
            // Check if this is an info icon popover
            if (popoverTriggerEl.classList.contains('info-icon')) {
                return new bootstrap.Popover(popoverTriggerEl, {
                    html: true,
                    trigger: 'hover focus',
                    placement: 'top',
                    container: 'body',
                    sanitize: false,
                    allowList: {
                        // Allow common HTML tags
                        'strong': [],
                        'b': [],
                        'em': [],
                        'i': [],
                        'br': [],
                        'p': [],
                        'a': ['href', 'title'],
                        'div': ['class'],
                        'span': ['class']
                    },
                    delay: { show: 200, hide: 100 }
                });
            }
        });

        // Prevent info icon popovers from closing when hovering over the popover itself
        jQuery(document).on('mouseenter', '.popover', function() {
            var popoverId = jQuery(this).attr('id');
            var triggerElement = jQuery('[aria-describedby="' + popoverId + '"]');
            if (triggerElement.hasClass('info-icon')) {
                // Keep the popover open while hovering over it
                clearTimeout(triggerElement.data('popover-timeout'));
            }
        });

        jQuery(document).on('mouseleave', '.popover', function() {
            var popoverId = jQuery(this).attr('id');
            var triggerElement = jQuery('[aria-describedby="' + popoverId + '"]');
            if (triggerElement.hasClass('info-icon')) {
                // Set a timeout to hide the popover after leaving
                var timeout = setTimeout(function() {
                    var popoverInstance = bootstrap.Popover.getInstance(triggerElement[0]);
                    if (popoverInstance) {
                        popoverInstance.hide();
                    }
                }, 100);
                triggerElement.data('popover-timeout', timeout);
            }
        });
    </script>
</body>
</html>
