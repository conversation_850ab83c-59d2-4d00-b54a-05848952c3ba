/*==============================================================================
// File:        _icon.scss
// Package:     Joomla / Mr Zen
// Synopsis:    Shared icon component - SASS
//============================================================================*/

.zen-icon {
	font-size: $icon-size-df;
	padding: 0;

/*==============================================================================
// Modifiers
//============================================================================*/

	@include modifier("default") {
		color: $text
	}

	@include modifier("default-light") {
		color: $base-grey
	}

	@include modifier("default-lighter") {
		color: $base-grey-light
	}

	@include modifier("light") {
		color: $base-white
	}

	@include modifier("primary") {
		color: $primary
	}

	@include modifier("helper") {
		margin: 0 0 0 -1px;
    position: relative;
		font-size: 10px;
		color: $text;
		top: -3px
	}

	/*------------------------------------------------------------------------------
	// Font Size Changes
	//----------------------------------------------------------------------------*/

	@include modifier("text-xxl") {
		font-size: $icon-size-xxl
	}

	@include modifier("text-xl") {
		font-size: $icon-size-xl
	}

	@include modifier("text-lg") {
		font-size: $icon-size-lg
	}

	@include modifier("text-md") {
		font-size: $icon-size-md
	}

	@include modifier("text-sm") {
		font-size: $icon-size-sm
	}

	@include modifier("text-xs") {
		font-size: $icon-size-xs
	}

	@include modifier("text-xxs") {
		font-size: $icon-size-xxs
	}
}

/*------------------------------------------------------------------------------
// Info Icon Component for Popovers
//----------------------------------------------------------------------------*/

.info-icon {
	font-size: 16px;
	color: $primary;
	cursor: pointer;
	margin-left: 4px;
	vertical-align: baseline;
	transition: color 0.2s ease;

	&:hover {
		color: $primary-mix-light;
	}

	// Ensure proper spacing in different contexts
	&:not(:first-child) {
		margin-left: 4px;
	}

	&:not(:last-child) {
		margin-right: 2px;
	}
}

/*------------------------------------------------------------------------------
// Popover Content Styling
//----------------------------------------------------------------------------*/

.popover {
	.popover-content {
		line-height: 1.4;

		// Style for optional title above the line
		.popover-title {
			font-weight: 600;
			margin-bottom: 8px;
			padding-bottom: 8px;
			border-bottom: 1px solid rgba(0, 0, 0, 0.1);
		}

		// Ensure proper spacing for HTML content
		p {
			margin-bottom: 8px;

			&:last-child {
				margin-bottom: 0;
			}
		}

		strong, b {
			font-weight: 600;
		}

		em, i {
			font-style: italic;
		}

		br {
			line-height: 1.6;
		}
	}
}

/*------------------------------------------------------------------------------
// Extend icon element & modifiers
//----------------------------------------------------------------------------*/

%zen-icon {
	@extend .zen-icon;
}
