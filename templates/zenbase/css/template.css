/* Bootstrap helpers required */
/* Base styles */
/*==============================================================================
// File:        _webkit.scss
// Package:     Joomla / Mr Zen
// Synopsis:    Extra webkit styles for compatibility - SASS
//============================================================================*/
.row:not(.justify-content-center) .sppb-col-auto,
.row:not(.justify-content-center) .col-auto {
  -webkit-flex: 0 0 auto; }

.row:not(.justify-content-center) .sppb-col-1,
.row:not(.justify-content-center) .col-1 {
  -webkit-flex: 0 0 8.333333%; }

.row:not(.justify-content-center) .sppb-col-2,
.row:not(.justify-content-center) .col-2 {
  -webkit-flex: 0 0 16.666667%; }

.row:not(.justify-content-center) .sppb-col-3,
.row:not(.justify-content-center) .col-3 {
  -webkit-flex: 0 0 25%; }

.row:not(.justify-content-center) .sppb-col-4,
.row:not(.justify-content-center) .col-4 {
  -webkit-flex: 0 0 33.333333%; }

.row:not(.justify-content-center) .sppb-col-5,
.row:not(.justify-content-center) .col-5 {
  -webkit-flex: 0 0 41.666667%; }

.row:not(.justify-content-center) .sppb-col-6,
.row:not(.justify-content-center) .col-6 {
  -webkit-flex: 0 0 50%; }

.row:not(.justify-content-center) .sppb-col-7,
.row:not(.justify-content-center) .col-7 {
  -webkit-flex: 0 0 58.333333%; }

.row:not(.justify-content-center) .sppb-col-8,
.row:not(.justify-content-center) .col-8 {
  -webkit-flex: 0 0 66.666667%; }

.row:not(.justify-content-center) .sppb-col-9,
.row:not(.justify-content-center) .col-9 {
  -webkit-flex: 0 0 75%; }

.row:not(.justify-content-center) .sppb-col-10,
.row:not(.justify-content-center) .col-10 {
  -webkit-flex: 0 0 83.333333%; }

.row:not(.justify-content-center) .sppb-col-11,
.row:not(.justify-content-center) .col-11 {
  -webkit-flex: 0 0 91.666667%; }

.row:not(.justify-content-center) .sppb-col-12,
.row:not(.justify-content-center) .col-12 {
  -webkit-flex: 0 0 100%; }

@media (max-width: 767.98px) {
  .row:not(.justify-content-center) .sppb-col-xs-auto {
    -webkit-flex: 0 0 auto; }
  .row:not(.justify-content-center) .sppb-col-xs-1 {
    -webkit-flex: 0 0 8.333333%; }
  .row:not(.justify-content-center) .sppb-col-xs-2 {
    -webkit-flex: 0 0 16.666667%; }
  .row:not(.justify-content-center) .sppb-col-xs-3 {
    -webkit-flex: 0 0 25%; }
  .row:not(.justify-content-center) .sppb-col-xs-4 {
    -webkit-flex: 0 0 33.333333%; }
  .row:not(.justify-content-center) .sppb-col-xs-5 {
    -webkit-flex: 0 0 41.666667%; }
  .row:not(.justify-content-center) .sppb-col-xs-6 {
    -webkit-flex: 0 0 50%; }
  .row:not(.justify-content-center) .sppb-col-xs-7 {
    -webkit-flex: 0 0 58.333333%; }
  .row:not(.justify-content-center) .sppb-col-xs-8 {
    -webkit-flex: 0 0 66.666667%; }
  .row:not(.justify-content-center) .sppb-col-xs-9 {
    -webkit-flex: 0 0 75%; }
  .row:not(.justify-content-center) .sppb-col-xs-10 {
    -webkit-flex: 0 0 83.333333%; }
  .row:not(.justify-content-center) .sppb-col-xs-11 {
    -webkit-flex: 0 0 91.666667%; }
  .row:not(.justify-content-center) .sppb-col-xs-12 {
    -webkit-flex: 0 0 100%; } }

@media (min-width: 576px) {
  .row:not(.justify-content-center) .col-sm-auto {
    -webkit-flex: 0 0 auto; }
  .row:not(.justify-content-center) .sppb-col-sm-1,
  .row:not(.justify-content-center) .col-sm-1 {
    -webkit-flex: 0 0 8.333333%; }
  .row:not(.justify-content-center) .sppb-col-sm-2,
  .row:not(.justify-content-center) .col-sm-2 {
    -webkit-flex: 0 0 16.666667%; }
  .row:not(.justify-content-center) .sppb-col-sm-3,
  .row:not(.justify-content-center) .col-sm-3 {
    -webkit-flex: 0 0 25%; }
  .row:not(.justify-content-center) .sppb-col-sm-4,
  .row:not(.justify-content-center) .col-sm-4 {
    -webkit-flex: 0 0 33.333333%; }
  .row:not(.justify-content-center) .sppb-col-sm-5,
  .row:not(.justify-content-center) .col-sm-5 {
    -webkit-flex: 0 0 41.666667%; }
  .row:not(.justify-content-center) .sppb-col-sm-6,
  .row:not(.justify-content-center) .col-sm-6 {
    -webkit-flex: 0 0 50%; }
  .row:not(.justify-content-center) .sppb-col-sm-7,
  .row:not(.justify-content-center) .col-sm-7 {
    -webkit-flex: 0 0 58.333333%; }
  .row:not(.justify-content-center) .sppb-col-sm-8,
  .row:not(.justify-content-center) .col-sm-8 {
    -webkit-flex: 0 0 66.666667%; }
  .row:not(.justify-content-center) .sppb-col-sm-9,
  .row:not(.justify-content-center) .col-sm-9 {
    -webkit-flex: 0 0 75%; }
  .row:not(.justify-content-center) .sppb-col-sm-10,
  .row:not(.justify-content-center) .col-sm-10 {
    -webkit-flex: 0 0 83.333333%; }
  .row:not(.justify-content-center) .sppb-col-sm-11,
  .row:not(.justify-content-center) .col-sm-11 {
    -webkit-flex: 0 0 91.666667%; }
  .row:not(.justify-content-center) .sppb-col-sm-12,
  .row:not(.justify-content-center) .col-sm-12 {
    -webkit-flex: 0 0 100%; } }

@media (min-width: 768px) {
  .row:not(.justify-content-center) .sppb-col-sm-auto,
  .row:not(.justify-content-center) .col-md-auto {
    -webkit-flex: 0 0 auto; }
  .row:not(.justify-content-center) .sppb-col-sm-1,
  .row:not(.justify-content-center) .col-md-1 {
    -webkit-flex: 0 0 8.333333%; }
  .row:not(.justify-content-center) .sppb-col-sm-2,
  .row:not(.justify-content-center) .col-md-2 {
    -webkit-flex: 0 0 16.666667%; }
  .row:not(.justify-content-center) .sppb-col-sm-3,
  .row:not(.justify-content-center) .col-md-3 {
    -webkit-flex: 0 0 25%; }
  .row:not(.justify-content-center) .sppb-col-sm-4,
  .row:not(.justify-content-center) .col-md-4 {
    -webkit-flex: 0 0 33.333333%; }
  .row:not(.justify-content-center) .sppb-col-sm-5,
  .row:not(.justify-content-center) .col-md-5 {
    -webkit-flex: 0 0 41.666667%; }
  .row:not(.justify-content-center) .sppb-col-sm-6,
  .row:not(.justify-content-center) .col-md-6 {
    -webkit-flex: 0 0 50%; }
  .row:not(.justify-content-center) .sppb-col-sm-7,
  .row:not(.justify-content-center) .col-md-7 {
    -webkit-flex: 0 0 58.333333%; }
  .row:not(.justify-content-center) .sppb-col-sm-8,
  .row:not(.justify-content-center) .col-md-8 {
    -webkit-flex: 0 0 66.666667%; }
  .row:not(.justify-content-center) .sppb-col-sm-9,
  .row:not(.justify-content-center) .col-md-9 {
    -webkit-flex: 0 0 75%; }
  .row:not(.justify-content-center) .sppb-col-sm-10,
  .row:not(.justify-content-center) .col-md-10 {
    -webkit-flex: 0 0 83.333333%; }
  .row:not(.justify-content-center) .sppb-col-sm-11,
  .row:not(.justify-content-center) .col-md-11 {
    -webkit-flex: 0 0 91.666667%; }
  .row:not(.justify-content-center) .sppb-col-sm-12,
  .row:not(.justify-content-center) .col-md-12 {
    -webkit-flex: 0 0 100%; } }

@media (min-width: 992px) {
  .row:not(.justify-content-center) .sppb-col-md-auto,
  .row:not(.justify-content-center) .col-lg-auto {
    -webkit-flex: 0 0 auto; }
  .row:not(.justify-content-center) .sppb-col-md-1,
  .row:not(.justify-content-center) .col-lg-1 {
    -webkit-flex: 0 0 8.333333%; }
  .row:not(.justify-content-center) .sppb-col-md-2,
  .row:not(.justify-content-center) .col-lg-2 {
    -webkit-flex: 0 0 16.666667%; }
  .row:not(.justify-content-center) .sppb-col-md-3,
  .row:not(.justify-content-center) .col-lg-3 {
    -webkit-flex: 0 0 25%; }
  .row:not(.justify-content-center) .sppb-col-md-4,
  .row:not(.justify-content-center) .col-lg-4 {
    -webkit-flex: 0 0 33.333333%; }
  .row:not(.justify-content-center) .sppb-col-md-5,
  .row:not(.justify-content-center) .col-lg-5 {
    -webkit-flex: 0 0 41.666667%; }
  .row:not(.justify-content-center) .sppb-col-md-6,
  .row:not(.justify-content-center) .col-lg-6 {
    -webkit-flex: 0 0 50%; }
  .row:not(.justify-content-center) .sppb-col-md-7,
  .row:not(.justify-content-center) .col-lg-7 {
    -webkit-flex: 0 0 58.333333%; }
  .row:not(.justify-content-center) .sppb-col-md-8,
  .row:not(.justify-content-center) .col-lg-8 {
    -webkit-flex: 0 0 66.666667%; }
  .row:not(.justify-content-center) .sppb-col-md-9,
  .row:not(.justify-content-center) .col-lg-9 {
    -webkit-flex: 0 0 75%; }
  .row:not(.justify-content-center) .sppb-col-md-10,
  .row:not(.justify-content-center) .col-lg-10 {
    -webkit-flex: 0 0 83.333333%; }
  .row:not(.justify-content-center) .sppb-col-md-11,
  .row:not(.justify-content-center) .col-lg-11 {
    -webkit-flex: 0 0 91.666667%; }
  .row:not(.justify-content-center) .sppb-col-md-12,
  .row:not(.justify-content-center) .col-lg-12 {
    -webkit-flex: 0 0 100%; } }

@media (min-width: 1200px) {
  .row:not(.justify-content-center) .sppb-col-lg-auto,
  .row:not(.justify-content-center) .col-xl-auto {
    -webkit-flex: 0 0 auto; }
  .row:not(.justify-content-center) .sppb-col-lg-1,
  .row:not(.justify-content-center) .col-xl-1 {
    -webkit-flex: 0 0 8.333333%; }
  .row:not(.justify-content-center) .sppb-col-lg-2,
  .row:not(.justify-content-center) .col-xl-2 {
    -webkit-flex: 0 0 16.666667%; }
  .row:not(.justify-content-center) .sppb-col-lg-3,
  .row:not(.justify-content-center) .col-xl-3 {
    -webkit-flex: 0 0 25%; }
  .row:not(.justify-content-center) .sppb-col-lg-4,
  .row:not(.justify-content-center) .col-xl-4 {
    -webkit-flex: 0 0 33.333333%; }
  .row:not(.justify-content-center) .sppb-col-lg-5,
  .row:not(.justify-content-center) .col-xl-5 {
    -webkit-flex: 0 0 41.666667%; }
  .row:not(.justify-content-center) .sppb-col-lg-6,
  .row:not(.justify-content-center) .col-xl-6 {
    -webkit-flex: 0 0 50%; }
  .row:not(.justify-content-center) .sppb-col-lg-7,
  .row:not(.justify-content-center) .col-xl-7 {
    -webkit-flex: 0 0 58.333333%; }
  .row:not(.justify-content-center) .sppb-col-lg-8,
  .row:not(.justify-content-center) .col-xl-8 {
    -webkit-flex: 0 0 66.666667%; }
  .row:not(.justify-content-center) .sppb-col-lg-9,
  .row:not(.justify-content-center) .col-xl-9 {
    -webkit-flex: 0 0 75%; }
  .row:not(.justify-content-center) .sppb-col-lg-10,
  .row:not(.justify-content-center) .col-xl-10 {
    -webkit-flex: 0 0 83.333333%; }
  .row:not(.justify-content-center) .sppb-col-lg-11,
  .row:not(.justify-content-center) .col-xl-11 {
    -webkit-flex: 0 0 91.666667%; }
  .row:not(.justify-content-center) .sppb-col-lg-12,
  .row:not(.justify-content-center) .col-xl-12 {
    -webkit-flex: 0 0 100%; } }

.col {
  -webkit-flex-basis: 0;
  -webkit-flex-grow: 1; }

.row-cols-1 > *, .row-cols-2 > *, .row-cols-3 > *, .row-cols-4 > *, .row-cols-5 > *,
.row-cols-6 > *, .row-cols-7 > *, .row-cols-8 > *, .row-cols-9 > *, .row-cols-10 > *,
.row-cols-11 > *, .row-cols-12 > * {
  -webkit-flex-basis: auto !important; }

.sppb-row,
.row {
  -webkit-flex-wrap: wrap;
  display: -webkit-flex;
  display: flex; }

.sppb-instagram-images,
.sp-pagebuilder-row {
  display: -webkit-flex;
  display: flex; }

.sp-item.slider-content-vercally-center {
  -webkit-align-items: center;
  -webkit-flex-wrap: wrap;
  display: -webkit-flex; }

@media (min-width: 992px) {
  .d-lg-flex {
    display: -webkit-flex !important; } }

/*==============================================================================
// File:        _variables.scss
// Package:     Joomla / Mr Zen
// Synopsis:    Main variables & reusable items - SASS
//============================================================================*/
/*------------------------------------------------------------------------------
// Global Variables / Bootstrap 5 :root variables
//----------------------------------------------------------------------------*/
/*------------------------------------------------------------------------------
// Font Variables / Bootstrap 5 :root variables
//----------------------------------------------------------------------------*/
/*------------------------------------------------------------------------------
// Icon Variables
//----------------------------------------------------------------------------*/
/*------------------------------------------------------------------------------
// Layout Variables
//----------------------------------------------------------------------------*/
/*------------------------------------------------------------------------------
// Breakpoint Variables
//----------------------------------------------------------------------------*/
/* Bootstrap 4 Alternatives */
/*------------------------------------------------------------------------------
// z-index Variables
//----------------------------------------------------------------------------*/
/*==============================================================================
// File:        _mixins.scss
// Package:     Joomla / Mr Zen
// Synopsis:    Main mixins & reusable items - SASS
//============================================================================*/
/*------------------------------------------------------------------------------
// Mixins
//----------------------------------------------------------------------------*/
/*==============================================================================
// File:        _fonts.scss
// Package:     Joomla / Mr Zen
// Synopsis:    Main web font references - SASS
//============================================================================*/
/*------------------------------------------------------------------------------
// Web fonts
//----------------------------------------------------------------------------*/
@font-face {
  font-family: "Old Growth";
  src: url("../fonts/OldGrowth-Regular.eot");
  src: url("../fonts/OldGrowth-Regular.eot?#iefix") format("embedded-opentype"), url("../fonts/OldGrowth-Regular.woff") format("woff"), url("../fonts/OldGrowth-Regular.ttf") format("truetype"), url("../fonts/OldGrowth-Regular.svg#OldGrowth-Regular") format("svg");
  font-weight: normal;
  font-display: swap;
  font-style: normal; }

@font-face {
  font-family: "IBM Plex Sans SemiBold";
  src: url("../fonts/IBMPlexSans-SemiBold.eot");
  src: url("../fonts/IBMPlexSans-SemiBold.eot?#iefix") format("embedded-opentype"), url("../fonts/IBMPlexSans-SemiBold.woff") format("woff"), url("../fonts/IBMPlexSans-SemiBold.ttf") format("truetype"), url("../fonts/IBMPlexSans-SemiBold.svg#IBMPlexSans-SemiBold") format("svg");
  font-display: swap;
  font-style: normal;
  font-weight: 600; }

@font-face {
  font-family: "IBM Plex Sans";
  src: url("../fonts/IBMPlexSans-Light.eot");
  src: url("../fonts/IBMPlexSans-Light.eot?#iefix") format("embedded-opentype"), url("../fonts/IBMPlexSans-Light.woff") format("woff"), url("../fonts/IBMPlexSans-Light.ttf") format("truetype"), url("../fonts/IBMPlexSans-Light.svg#IBMPlexSans-Light") format("svg");
  font-style: normal;
  font-display: swap;
  font-weight: 300; }

/*------------------------------------------------------------------------------
// ZEN Icons - Source: "http://fontello.com/"
//----------------------------------------------------------------------------*/
@font-face {
  font-family: "fontello";
  src: url("../fonts/fontello.eot");
  src: url("../fonts/fontello.eot#iefix") format("embedded-opentype"), url("../fonts/fontello.woff") format("woff"), url("../fonts/fontello.ttf") format("truetype"), url("../fonts/fontello.svg#fontello") format("svg");
  font-weight: normal;
  font-display: swap;
  font-style: normal; }

.fontello, .zen-breadcrumbs .breadcrumb-item + .breadcrumb-item:before, .breadcrumb .breadcrumb-item + .breadcrumb-item:before, .zen-select__span:after, .selectlist span:after, .zen-content-search__filter-ordering select:after, .zen-btn--accordion-main:after, .zen-btn--accordion-sub:after, .zen-accordion__btn-sub:after, a.zen-btn--accordion-sub:not([tabindex]):not([href]):after, .zen-btn--accordion-mobile-menu:after, .zen-btn--accordion-mobile-submenu:after, .zen-accordion__btn-main:after, a.zen-btn--accordion-main:not([tabindex]):not([href]):after, .zen-list--default-ticks li:before, .zen-rte--default-list-ticks ul li:before, .zen-list--default-crosses li:before, .zen-rte--default-list-crosses ul li:before, .zen-link--filter-tag:after, .zen-cta__image-overlay-content--icon:before, .zen-menu .dropdown-toggle:after, .carousel-control-next .carousel-control-next-icon,
.carousel-control-next .carousel-control-prev-icon,
.carousel-control-prev .carousel-control-next-icon,
.carousel-control-prev .carousel-control-prev-icon, .sppb-testimonial-carousel-rating::before,
.sppb-testimonial-carousel-rating:after {
  /* Use important to prevent issues */
  font-family: "fontello" !important;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-variant: normal;
  text-transform: none;
  font-weight: normal;
  font-style: normal;
  line-height: 1;
  speak: none; }

.icon-altitude-down:before {
  content: "\e821"; }

.icon-altitude-up:before {
  content: "\e822"; }

.icon-arrow-down:before {
  content: "\e815"; }

.icon-arrow-left:before {
  content: "\e816"; }

.icon-arrow-right:before {
  content: "\e817"; }

.icon-arrow-up:before {
  content: "\e818"; }

.icon-ascent:before {
  content: "\e823"; }

.icon-biking:before {
  content: "\e84f"; }

.icon-boat:before {
  content: "\e850"; }

.icon-bus:before {
  content: "\e851"; }

.icon-calendar:before {
  content: "\e824"; }

.icon-car:before {
  content: "\e852"; }

.icon-caret-down:before {
  content: "\e819"; }

.icon-caret-left:before {
  content: "\e81a"; }

.icon-caret-right:before {
  content: "\e81b"; }

.icon-caret-up:before {
  content: "\e81c"; }

.icon-chevron-down:before {
  content: "\e81d"; }

.icon-chevron-left:before {
  content: "\e81e"; }

.icon-chevron-right:before {
  content: "\e81f"; }

.icon-chevron-up:before {
  content: "\e820"; }

.icon-clock:before {
  content: "\e825"; }

.icon-coach:before {
  content: "\e853"; }

.icon-cog:before {
  content: "\e826"; }

.icon-compass:before {
  content: "\e827"; }

.icon-covid:before {
  content: "\e828"; }

.icon-discount:before {
  content: "\e829"; }

.icon-double-bed:before {
  content: "\e82a"; }

.icon-download:before {
  content: "\e82b"; }

.icon-drinks:before {
  content: "\e82c"; }

.icon-ecycle:before {
  content: "\e82d"; }

.icon-email:before {
  content: "\e82e"; }

.icon-facebook:before {
  content: "\e847"; }

.icon-flight:before {
  content: "\e854"; }

.icon-footprint:before {
  content: "\e82f"; }

.icon-gallery:before {
  content: "\e830"; }

.icon-grade-five:before {
  content: "\e842"; }

.icon-grade-four:before {
  content: "\e843"; }

.icon-grade-one:before {
  content: "\e844"; }

.icon-grade-three:before {
  content: "\e845"; }

.icon-grade-two:before {
  content: "\e846"; }

.icon-helicopter:before {
  content: "\e855"; }

.icon-hotel:before {
  content: "\e831"; }

.icon-info:before {
  content: "\e832"; }

.icon-instagram:before {
  content: "\e848"; }

.icon-landing:before {
  content: "\e856"; }

.icon-link:before {
  content: "\e833"; }

.icon-location:before {
  content: "\e834"; }

.icon-luggage:before {
  content: "\e835"; }

.icon-medkit:before {
  content: "\e836"; }

.icon-no-flight:before {
  content: "\e857"; }

.icon-payment:before {
  content: "\e837"; }

.icon-pintrest:before {
  content: "\e849"; }

.icon-pool:before {
  content: "\e838"; }

.icon-save:before {
  content: "\e839"; }

.icon-security:before {
  content: "\e83a"; }

.icon-service:before {
  content: "\e83b"; }

.icon-sightseeing:before {
  content: "\e83c"; }

.icon-single-bed:before {
  content: "\e83d"; }

.icon-snapchat:before {
  content: "\e84a"; }

.icon-sys-close:before {
  content: "\e800"; }

.icon-sys-disable:before {
  content: "\e801"; }

.icon-sys-envelope:before {
  content: "\e802"; }

.icon-sys-hamburger:before {
  content: "\e803"; }

.icon-sys-heart-solid:before {
  content: "\e804"; }

.icon-sys-heart:before {
  content: "\e805"; }

.icon-sys-home-outline:before {
  content: "\e806"; }

.icon-sys-home-solid:before {
  content: "\e807"; }

.icon-sys-info:before {
  content: "\e808"; }

.icon-sys-login:before {
  content: "\e809"; }

.icon-sys-plus:before {
  content: "\e80a"; }

.icon-sys-save:before {
  content: "\e80b"; }

.icon-sys-search:before {
  content: "\e80c"; }

.icon-sys-share:before {
  content: "\e80d"; }

.icon-sys-star-outline-half:before {
  content: "\e80e"; }

.icon-sys-star-outline:before {
  content: "\e80f"; }

.icon-sys-star-solid-half:before {
  content: "\e810"; }

.icon-sys-star-solid:before {
  content: "\e811"; }

.icon-sys-telephone:before {
  content: "\e812"; }

.icon-sys-tick:before {
  content: "\e813"; }

.icon-sys-trash:before {
  content: "\e814"; }

.icon-tag:before {
  content: "\e83e"; }

.icon-takeoff:before {
  content: "\e858"; }

.icon-telephone:before {
  content: "\e84b"; }

.icon-ticket:before {
  content: "\e83f"; }

.icon-twitter:before {
  content: "\e84c"; }

.icon-user:before {
  content: "\e840"; }

.icon-villa:before {
  content: "\e841"; }

.icon-whatsapp:before {
  content: "\e84d"; }

.icon-youtube:before {
  content: "\e84e"; }

.icon-chevron-down-thin:before {
  content: "\e859"; }

.icon-chevron-left-thin:before {
  content: "\e85a"; }

.icon-chevron-right-thin:before {
  content: "\e85b"; }

.icon-chevron-up-thin:before {
  content: "\e85c"; }

.icon-tent:before {
  content: '\e866'; }

/*------------------------------------------------------------------------------
// Extend Icon Class
//----------------------------------------------------------------------------*/
/*==============================================================================
// File:        _type.scss
// Package:     Joomla / Mr Zen
// Synopsis:    Main type styling - SASS
//============================================================================*/
h1 {
  font-size: 26px;
  font-family: "Old Growth", Helvetica, Arial, sans-serif;
  text-transform: none;
  color: #23383C; }
  @media (min-width: 300px) {
    h1 {
      font-size: calc(26px + 10 * (100vw - 300px) / 1300); } }
  @media (min-width: 1600px) {
    h1 {
      font-size: 36px; } }
  h1 strong,
  h1 b {
    font-size: 26px;
    font-family: "Old Growth", Helvetica, Arial, sans-serif;
    text-transform: none;
    font-weight: normal; }
    @media (min-width: 300px) {
      h1 strong,
      h1 b {
        font-size: calc(26px + 10 * (100vw - 300px) / 1300); } }
    @media (min-width: 1600px) {
      h1 strong,
      h1 b {
        font-size: 36px; } }

h2 {
  font-size: 22px;
  font-family: "Old Growth", Helvetica, Arial, sans-serif;
  text-transform: none;
  color: #23383C; }
  @media (min-width: 300px) {
    h2 {
      font-size: calc(22px + 6 * (100vw - 300px) / 1300); } }
  @media (min-width: 1600px) {
    h2 {
      font-size: 28px; } }
  h2 strong,
  h2 b {
    font-size: 22px;
    font-family: "Old Growth", Helvetica, Arial, sans-serif;
    text-transform: none;
    font-weight: normal; }
    @media (min-width: 300px) {
      h2 strong,
      h2 b {
        font-size: calc(22px + 6 * (100vw - 300px) / 1300); } }
    @media (min-width: 1600px) {
      h2 strong,
      h2 b {
        font-size: 28px; } }

h3, .zen-search__title {
  font-size: 20px;
  font-family: "Old Growth", Helvetica, Arial, sans-serif;
  text-transform: none;
  color: #23383C; }
  @media (min-width: 300px) {
    h3, .zen-search__title {
      font-size: calc(20px + 4 * (100vw - 300px) / 1300); } }
  @media (min-width: 1600px) {
    h3, .zen-search__title {
      font-size: 24px; } }
  h3 strong, .zen-search__title strong,
  h3 b, .zen-search__title b {
    font-size: 20px;
    font-family: "Old Growth", Helvetica, Arial, sans-serif;
    text-transform: none;
    font-weight: normal; }
    @media (min-width: 300px) {
      h3 strong, .zen-search__title strong,
      h3 b, .zen-search__title b {
        font-size: calc(20px + 4 * (100vw - 300px) / 1300); } }
    @media (min-width: 1600px) {
      h3 strong, .zen-search__title strong,
      h3 b, .zen-search__title b {
        font-size: 24px; } }

h4 {
  font-size: 18px;
  font-family: "Old Growth", Helvetica, Arial, sans-serif;
  text-transform: none;
  color: #23383C; }
  @media (min-width: 300px) {
    h4 {
      font-size: calc(18px + 4 * (100vw - 300px) / 1300); } }
  @media (min-width: 1600px) {
    h4 {
      font-size: 22px; } }
  h4 strong,
  h4 b {
    font-size: 18px;
    font-family: "Old Growth", Helvetica, Arial, sans-serif;
    text-transform: none;
    font-weight: normal; }
    @media (min-width: 300px) {
      h4 strong,
      h4 b {
        font-size: calc(18px + 4 * (100vw - 300px) / 1300); } }
    @media (min-width: 1600px) {
      h4 strong,
      h4 b {
        font-size: 22px; } }

h5 {
  font-size: 16px;
  font-family: "Old Growth", Helvetica, Arial, sans-serif;
  text-transform: none;
  color: #23383C; }
  @media (min-width: 300px) {
    h5 {
      font-size: calc(16px + 2 * (100vw - 300px) / 1300); } }
  @media (min-width: 1600px) {
    h5 {
      font-size: 18px; } }
  h5 strong,
  h5 b {
    font-size: 16px;
    font-family: "Old Growth", Helvetica, Arial, sans-serif;
    text-transform: none;
    font-weight: normal; }
    @media (min-width: 300px) {
      h5 strong,
      h5 b {
        font-size: calc(16px + 2 * (100vw - 300px) / 1300); } }
    @media (min-width: 1600px) {
      h5 strong,
      h5 b {
        font-size: 18px; } }

h6 {
  font-size: 15px;
  font-family: "Old Growth", Helvetica, Arial, sans-serif;
  text-transform: none;
  color: #23383C; }
  @media (min-width: 300px) {
    h6 {
      font-size: calc(15px + 2 * (100vw - 300px) / 1300); } }
  @media (min-width: 1600px) {
    h6 {
      font-size: 17px; } }
  h6 strong,
  h6 b {
    font-size: 15px;
    font-family: "Old Growth", Helvetica, Arial, sans-serif;
    text-transform: none;
    font-weight: normal; }
    @media (min-width: 300px) {
      h6 strong,
      h6 b {
        font-size: calc(15px + 2 * (100vw - 300px) / 1300); } }
    @media (min-width: 1600px) {
      h6 strong,
      h6 b {
        font-size: 17px; } }

/*------------------------------------------------------------------------------
// Extend heading tags
//----------------------------------------------------------------------------*/
/* Shared component styles */
/*==============================================================================
// File:        _breadcrumbs.scss
// Package:     Joomla / Mr Zen
// Synopsis:    Shared breadcrumbs component - SASS
//============================================================================*/
.zen-breadcrumbs, .breadcrumb {
  font-family: "IBM Plex Sans SemiBold", Helvetica, Arial, sans-serif;
  text-transform: uppercase;
  text-overflow: ellipsis;
  display: inline-block;
  z-index: 10;
  white-space: nowrap;
  line-height: 1.35em;
  position: absolute;
  overflow: hidden;
  max-height: 60px;
  background: none;
  padding: 20px 0;
  width: 100%;
  margin: 0;
  /*==============================================================================
// Modifiers
//============================================================================*/ }
  .zen-breadcrumbs .breadcrumb-item, .breadcrumb .breadcrumb-item {
    display: inline; }
    .zen-breadcrumbs .breadcrumb-item + .breadcrumb-item, .breadcrumb .breadcrumb-item + .breadcrumb-item {
      padding-left: .3rem; }
      .zen-breadcrumbs .breadcrumb-item + .breadcrumb-item:before, .breadcrumb .breadcrumb-item + .breadcrumb-item:before {
        font-size: 12px;
        padding-right: .3rem;
        content: "\e81f";
        float: none; }
  .zen-breadcrumbs--light, .zen-body__breadcrumbs .light-breadcrumbs .zen-breadcrumbs, .zen-body__breadcrumbs .light-breadcrumbs .breadcrumb {
    text-shadow: 1px 1px 5px #262626;
    color: #FFF;
    margin: 20px 0;
    padding: 0; }
    .zen-breadcrumbs--light .breadcrumb-item:before, .zen-body__breadcrumbs .light-breadcrumbs .zen-breadcrumbs .breadcrumb-item:before, .zen-body__breadcrumbs .light-breadcrumbs .breadcrumb .breadcrumb-item:before {
      color: #FFF; }
    .zen-breadcrumbs--light .breadcrumb-item.active, .zen-body__breadcrumbs .light-breadcrumbs .zen-breadcrumbs .breadcrumb-item.active, .zen-body__breadcrumbs .light-breadcrumbs .breadcrumb .breadcrumb-item.active {
      color: #FFF; }

/*------------------------------------------------------------------------------
// Extend breadcrumbs element & modifiers
//----------------------------------------------------------------------------*/
/*==============================================================================
// File:        _article.scss
// Package:     Joomla / Mr Zen
// Synopsis:    Shared article component - SASS
//============================================================================*/
/*==============================================================================
// Main Zen Article for SPPB Articles Addon and Blog Main Top Cards
//============================================================================*/
.zen-article, .sppb-addon-article {
  position: relative;
  max-width: 530px;
  padding: 10px;
  margin: 0 auto;
  /*==============================================================================
// Elements
//============================================================================*/ }
  .zen-article__image, .sppb-article-img-wrap {
    margin: 0 0 5px; }
  .zen-article__top, .sppb-article-top-wrap {
    background: rgba(4, 138, 187, 0.7);
    justify-content: flex-start;
    flex-direction: column;
    display: -webkit-flex;
    z-index: 10;
    position: absolute;
    padding: 20px;
    display: flex;
    right: 5px;
    left: 5px;
    top: 5px; }
  .zen-article__info-wrap, .sppb-article-info-wrap {
    position: absolute;
    bottom: 20px;
    right: 20px; }
  .zen-article__tags, .sppb-article-meta {
    text-transform: uppercase;
    padding: 0;
    margin: 0; }
  .zen-article__content, .sppb-article-content {
    flex-direction: column;
    display: -webkit-flex;
    position: relative;
    min-height: 220px;
    padding: 5px;
    display: flex; }

/*==============================================================================
// Zen Article Secondary (for Blog pages that are not featured)
//============================================================================*/
.zen-article-secondary {
  text-decoration: none;
  margin-bottom: 20px;
  padding: 15px;
  /*==============================================================================
// Elements
//============================================================================*/ }
  .zen-article-secondary__image {
    margin: 0 0 5px; }
  .zen-article-secondary__wrap {
    justify-content: space-between;
    flex-direction: column;
    display: -webkit-flex;
    display: flex; }
  .zen-article-secondary__cta {
    align-self: flex-end; }
  .zen-article-secondary__title {
    margin-top: 10px; }
  .zen-article-secondary__tags {
    text-transform: uppercase;
    color: #8F8F8F; }
  .zen-article-secondary__body {
    color: #23383C; }

/*------------------------------------------------------------------------------
// Extend article element & modifiers
//----------------------------------------------------------------------------*/
/*------------------------------------------------------------------------------
// Additional
//----------------------------------------------------------------------------*/
.article-info-term {
  display: none; }

/*==============================================================================
// File:        _badge.scss
// Package:     Joomla / Mr Zen
// Synopsis:    Shared badge component - SASS
//============================================================================*/
.zen-badge {
  vertical-align: baseline;
  display: inline-block;
  background: #0095CC;
  padding: .25em .55em;
  white-space: nowrap;
  border-radius: 50%;
  position: relative;
  color: #FFF;
  text-align: center;
  font-weight: bold;
  line-height: 1em;
  font-size: 75%;
  top: -2px; }

/*==============================================================================
// File:        _media.scss
// Package:     Joomla / Mr Zen
// Synopsis:    Shared media component - SASS
//============================================================================*/
.zen-media {
  background: #F2F2F2;
  border: 1px solid #C6C6C6;
  padding: 30px 50px;
  margin: 0 0 15px;
  border-radius: 0;
  /*==============================================================================
// Elements
//============================================================================*/ }
  @media (max-width: 575.98px) {
    .zen-media {
      text-align: center; } }
  .zen-media__icon {
    padding: 0 10px;
    font-size: 40px;
    color: #23383C; }
  .zen-media__body {
    margin: 0 0 0 10px; }
  .zen-media__title {
    margin: 0 0 10px;
    padding: 0; }
  .zen-media__content {
    font-size: 16px;
    font-family: "IBM Plex Sans", Helvetica, Arial, sans-serif;
    color: #0095CC; }
    @media (min-width: 300px) {
      .zen-media__content {
        font-size: calc(16px + 2 * (100vw - 300px) / 1300); } }
    @media (min-width: 1600px) {
      .zen-media__content {
        font-size: 18px; } }
    .zen-media__content p {
      display: inline-block; }

/*==============================================================================
// File:        _panel.scss
// Package:     Joomla / Mr Zen
// Synopsis:    Shared panel component - SASS
//============================================================================*/
.zen-panel {
  -webkit-transition: all 1s ease 0.5s;
  -moz-transition: all 1s ease 0.5s;
  -ms-transition: all 1s ease 0.5s;
  -o-transition: all 1s ease 0.5s;
  transition: all 1s ease 0.5s;
  z-index: 20;
  background: #F2F2F2;
  visibility: hidden;
  position: fixed;
  padding: 15px;
  opacity: 0;
  /*==============================================================================
// Elements
//============================================================================*/
  /*==============================================================================
// Modifiers
//============================================================================*/ }
  .zen-panel.show-panel {
    visibility: visible;
    opacity: 1; }
  @media (max-width: 1199.98px) {
    .zen-panel__content {
      font-size: 15px; } }
  @media (max-width: 767.98px) {
    .zen-panel__content {
      font-size: 12px; } }
  .zen-panel--policy {
    border-top: 1px solid #C6C6C6;
    bottom: -200px;
    width: 100%; }
    .zen-panel--policy.show-panel {
      bottom: 0; }

/*------------------------------------------------------------------------------
// Extend panel element & modifiers
//----------------------------------------------------------------------------*/
/*==============================================================================
// File:        _menu.scss
// Package:     Joomla / Mr Zen
// Synopsis:    Shared menu component - SASS
//============================================================================*/
.zen-menu {
  position: relative;
  height: 100%;
  /*==============================================================================
// Elements
//============================================================================*/ }
  .zen-menu__main {
    height: 100%;
    padding: 0; }
    .zen-menu__main .zen-list--menu-dropdown {
      top: 108px; }
    .zen-menu__main .nav-header,
    .zen-menu__main .nav-link {
      text-transform: uppercase; }
    .zen-menu__main .sub-item {
      display: inline-block;
      padding: 5px 10px; }
    @media (max-width: 1199.98px) {
      .zen-menu__main {
        -webkit-transition: all 0.3s ease-in-out 0s;
        -moz-transition: all 0.3s ease-in-out 0s;
        -ms-transition: all 0.3s ease-in-out 0s;
        -o-transition: all 0.3s ease-in-out 0s;
        transition: all 0.3s ease-in-out 0s;
        border-left: 6px solid #0095CC;
        background: #FFF;
        z-index: 30;
        font-weight: normal;
        min-width: 300px;
        position: fixed;
        overflow: auto;
        max-width: 85%;
        right: -300px;
        height: 100%;
        opacity: 0;
        margin: 0;
        top: 0; }
        .zen-menu__main .container,
        .zen-menu__main .col-12,
        .zen-menu__main .row {
          padding: 0;
          margin: 0; } }
  .zen-menu__main-mobile {
    overflow: auto;
    padding: 10px;
    height: 95vh;
    list-style: none; }
  .zen-menu .menu-accord-zen-list {
    margin-left: 10px;
    padding-left: 10px; }
  .zen-menu .menu-sub-accord-zen-list {
    margin-left: 0;
    padding-left: 0;
    border: 1px solid lightgrey;
    margin-top: 10px;
    margin-bottom: 10px;
    background: white; }
  .zen-menu .menu-sub-sub-accord-zen-list {
    padding-left: 0;
    margin-left: 0; }
    .zen-menu .menu-sub-sub-accord-zen-list li {
      padding: 5px 10px;
      max-width: 220px; }
    .zen-menu .menu-sub-sub-accord-zen-list a {
      padding: 0;
      max-width: 220px;
      font-size: 15px; }
  .zen-menu__mega-tab-dropdown {
    -webkit-box-shadow: inset 0 10px 15px -8px rgba(38, 38, 38, 0.3);
    -moz-box-shadow: inset 0 10px 15px -8px rgba(38, 38, 38, 0.3);
    box-shadow: inset 0 10px 15px -8px rgba(38, 38, 38, 0.3);
    background: #FFF;
    min-height: 700px;
    position: absolute;
    margin: 0 auto;
    border: none;
    width: 940px;
    top: 106px;
    padding: 0; }
    .zen-menu__mega-tab-dropdown .nav-tabs {
      -webkit-box-shadow: inset 0 10px 15px -8px rgba(38, 38, 38, 0.3);
      -moz-box-shadow: inset 0 10px 15px -8px rgba(38, 38, 38, 0.3);
      box-shadow: inset 0 10px 15px -8px rgba(38, 38, 38, 0.3);
      background: #F2F2F2;
      position: absolute;
      width: 250px;
      bottom: 0;
      left: 0;
      top: 0; }
    .zen-menu__mega-tab-dropdown .nav {
      display: unset; }
  .zen-menu__mega-tab-content {
    padding: 30px 0 20px;
    position: absolute;
    height: 100%;
    width: 692px;
    z-index: -1;
    left: 100%;
    top: 0; }
    .zen-menu__mega-tab-content .head-sec {
      border-bottom: 1px solid #C6C6C6;
      justify-content: space-between;
      padding-bottom: 15px;
      display: -webkit-flex;
      display: flex; }
    .zen-menu__mega-tab-content .main-items {
      margin: 0; }
    .zen-menu__mega-tab-content.active {
      visibility: visible;
      z-index: 1;
      opacity: 1; }
      .zen-menu__mega-tab-content.active .tab-pane {
        display: block;
        opacity: 1; }

/*------------------------------------------------------------------------------
// Extend menu element & modifiers
//----------------------------------------------------------------------------*/
/*------------------------------------------------------------------------------
// Mobile menu features
//----------------------------------------------------------------------------*/
@media (max-width: 1199.98px) {
  .zen-menu-open {
    max-height: 100vh;
    overflow: hidden; }
    .zen-menu-open .zen-overlay {
      display: block;
      opacity: 1; }
    .zen-menu-open .zen-header__main {
      z-index: auto; }
    .zen-menu-open .zen-menu__main {
      -webkit-transform: translateX(-300px);
      -moz-transform: translateX(-300px);
      -ms-transform: translateX(-300px);
      -o-transform: translateX(-300px);
      transform: translateX(-300px);
      opacity: 1; } }

.sticky-marker {
  position: absolute;
  right: 10px;
  height: 1px;
  width: 1px;
  bottom: 0; }

.scroll-transition {
  margin-top: 97px; }

.zen-header__main.fixed-menu {
  -webkit-transition: all 0.2s ease 0s;
  -moz-transition: all 0.2s ease 0s;
  -ms-transition: all 0.2s ease 0s;
  -o-transition: all 0.2s ease 0s;
  transition: all 0.2s ease 0s;
  background: #FFF;
  position: fixed;
  width: 100%;
  z-index: 40;
  top: 0; }

/*==============================================================================
// File:        _modal.scss
// Package:     Joomla / Mr Zen
// Synopsis:    Shared modal component - SASS
//============================================================================*/
.zen-modal, .modal {
  margin: 0;
  /*==============================================================================
// Modifiers
//============================================================================*/
  /*==============================================================================
// Elements
//============================================================================*/ }
  .zen-modal--gallery {
    background: #262626; }
  .zen-modal__close-btn, .modal-close {
    position: absolute;
    right: 15px;
    top: 15px; }
    .zen-modal__close-btn i, .modal-close i {
      padding: 0 5px; }
      .zen-modal__close-btn i:hover, .modal-close i:hover, .zen-modal__close-btn i:focus, .modal-close i:focus {
        cursor: pointer;
        opacity: .85; }
  .zen-modal__content, .modal-content {
    border-radius: 0;
    border: none; }
  .zen-modal__body, .modal-body {
    background: #FFF;
    padding: 7px; }
  .zen-modal__footer, .modal-footer {
    justify-content: flex-start;
    color: #FFF;
    padding: 10px 0;
    border: none; }

/*------------------------------------------------------------------------------
// Extend modal element & modifiers
//----------------------------------------------------------------------------*/
/*==============================================================================
// File:        _hero.scss
// Package:     Joomla / Mr Zen
// Synopsis:    Shared hero component - SASS
//============================================================================*/
.zen-hero {
  position: relative;
  /*==============================================================================
// Elements
//============================================================================*/ }
  .zen-hero__title {
    padding: 0; }
  .zen-hero__absolute-title {
    z-index: 5;
    position: absolute;
    bottom: 0;
    right: 0;
    left: 0;
    top: 0; }
  .zen-hero__relative-title {
    position: relative;
    padding: 0 0 30px;
    margin: 30px 0; }
  .zen-hero__title-center {
    -webkit-align-items: center;
    -webkit-flex-flow: column;
    justify-content: center;
    display: -webkit-flex;
    align-items: center;
    flex-flow: column;
    max-width: 1400px;
    padding: 0 15px;
    margin: 0 auto;
    display: flex;
    height: 100%; }
  .zen-hero__title-center-holiday {
    -webkit-align-items: center;
    -webkit-flex-flow: column;
    justify-content: center;
    display: -webkit-flex;
    align-items: center;
    padding: 10% 15px;
    flex-flow: column;
    max-width: 90vw;
    margin: 0 auto;
    display: flex;
    height: 100%; }
    @media (max-width: 575.98px) {
      .zen-hero__title-center-holiday {
        padding: 10% 0; } }
  .zen-hero__picture-container {
    padding-top: 31.25%;
    background-size: contain;
    position: relative;
    overflow: hidden;
    display: block;
    content: "";
    bottom: 0;
    right: 0;
    left: 0;
    top: 0; }
    .zen-hero__picture-container:before {
      content: "";
      display: block;
      position: absolute;
      background: transparent;
      /* Rollback */
      background: linear-gradient(180deg, transparent 0%, rgba(0, 0, 0, 0.2) 49.25%, transparent 100%);
      z-index: 1;
      bottom: 0;
      right: 0;
      left: 0;
      top: 0; }
    @media (max-width: 1399.98px) {
      .zen-hero__picture-container {
        padding-top: 43.75%; } }
    @media (max-width: 991.98px) {
      .zen-hero__picture-container {
        padding-top: 68.75%; } }
    @media (max-width: 767.98px) {
      .zen-hero__picture-container {
        padding-top: 81.25%; } }
    @media (max-width: 575.98px) {
      .zen-hero__picture-container {
        padding-top: 93.75%; } }
    @media (max-width: 400px) {
      .zen-hero__picture-container {
        padding-top: 100%; } }
  .zen-hero__picture {
    position: absolute;
    left: 0;
    top: 0; }

/*==============================================================================
// File:        _card.scss
// Package:     Joomla / Mr Zen
// Synopsis:    Shared card component - SASS
//============================================================================*/
.zen-card, .zen-article, .sppb-addon-article, .zen-article-secondary, .card, .sppb-testimonial-carousel-testi_layout1 .sppb-carousel-extended-item {
  border: 1px solid #C6C6C6;
  background: #FFF;
  border-radius: 0;
  /*==============================================================================
// Elements
//============================================================================*/
  /*==============================================================================
// Modifiers
//============================================================================*/ }
  .zen-card__image, .zen-article__image, .sppb-article-img-wrap, .zen-article-secondary__image, .card-image {
    -webkit-align-items: center;
    justify-content: center;
    display: -webkit-flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    display: flex;
    padding: 0;
    margin: 0; }
    .zen-card__image--max-height {
      max-height: 275px;
      overflow: hidden; }
  .zen-card__image-overlay {
    font-size: 11px;
    font-family: "IBM Plex Sans SemiBold", Helvetica, Arial, sans-serif;
    background: rgba(0, 149, 204, 0.7);
    -webkit-align-items: center;
    text-transform: uppercase;
    justify-content: center;
    display: -webkit-flex;
    align-items: center;
    position: absolute;
    display: flex;
    padding: 10px;
    width: 100%;
    bottom: 0; }
    @media (min-width: 300px) {
      .zen-card__image-overlay {
        font-size: calc(11px + 2 * (100vw - 300px) / 1300); } }
    @media (min-width: 1600px) {
      .zen-card__image-overlay {
        font-size: 13px; } }
  .zen-card__title, .card-title {
    font-family: "IBM Plex Sans SemiBold", Helvetica, Arial, sans-serif;
    margin: 20px 0; }
  .zen-card__header, .card-header {
    background: #F2F2F2;
    border-radius: 0;
    border: none; }
  .zen-card__body, .card-body {
    padding: 15px; }
    .zen-card__body .grade-image, .card-body .grade-image {
      max-height: 37px;
      max-width: 37px; }
  .zen-card__content {
    border-top: 1px solid #657477;
    padding: 10px 0; }
  .zen-card__info, .card-info {
    font-family: "IBM Plex Sans SemiBold", Helvetica, Arial, sans-serif;
    border-bottom: 1px solid #C6C6C6;
    border-top: 1px solid #C6C6C6;
    margin: 10px 0 15px;
    padding: 7px 5px; }
  .zen-card__footer, .card-footer {
    padding: 0; }
  .zen-card--related {
    border-radius: 4px;
    margin: 0 auto 30px;
    max-width: 545px; }
  @media (max-width: 767.98px) {
    .zen-card--search, .card-wide {
      max-width: 450px;
      margin: 0 auto; } }
  .zen-card--review {
    margin: 0 auto 30px;
    text-align: center;
    max-width: 650px; }
    .zen-card--review .rce-closed {
      -webkit-transition: all 0.5s ease 0s;
      -moz-transition: all 0.5s ease 0s;
      -ms-transition: all 0.5s ease 0s;
      -o-transition: all 0.5s ease 0s;
      transition: all 0.5s ease 0s;
      visibility: visible;
      margin-bottom: 0;
      opacity: 1; }
    .zen-card--review .rce-open {
      -webkit-transition: all 0.5s ease 0s;
      -moz-transition: all 0.5s ease 0s;
      -ms-transition: all 0.5s ease 0s;
      -o-transition: all 0.5s ease 0s;
      transition: all 0.5s ease 0s;
      visibility: hidden;
      opacity: 0; }
    .zen-card--review.active-review .rce-closed {
      visibility: hidden;
      margin-bottom: -5%;
      opacity: 0; }
      @media (max-width: 575.98px) {
        .zen-card--review.active-review .rce-closed {
          margin-bottom: -15%; } }
      @media (max-width: 400px) {
        .zen-card--review.active-review .rce-closed {
          margin-bottom: -20%; } }
    .zen-card--review.active-review .rce-open {
      visibility: visible;
      opacity: 1; }
  .zen-card--exclusive {
    border: 1px solid #0095CC; }
  .zen-card--note {
    padding: 30px 50px; }
  .zen-card--full-height {
    /* Rollback Display */
    display: block;
    display: grid;
    height: 100%; }

/*------------------------------------------------------------------------------
// Extend menu element & modifiers
//----------------------------------------------------------------------------*/
/*==============================================================================
// File:        _icon.scss
// Package:     Joomla / Mr Zen
// Synopsis:    Shared icon component - SASS
//============================================================================*/
.zen-icon, .sppb-testimonial-carousel-rating::before,
.sppb-testimonial-carousel-rating:after {
  font-size: 30px;
  padding: 0;
  /*==============================================================================
// Modifiers
//============================================================================*/
  /*------------------------------------------------------------------------------
	// Font Size Changes
	//----------------------------------------------------------------------------*/ }
  .zen-icon--default {
    color: #23383C; }
  .zen-icon--default-light {
    color: #C6C6C6; }
  .zen-icon--default-lighter {
    color: #DDD; }
  .zen-icon--light {
    color: #FFF; }
  .zen-icon--primary {
    color: #0095CC; }
  .zen-icon--helper {
    margin: 0 0 0 -1px;
    position: relative;
    font-size: 10px;
    color: #23383C;
    top: -3px; }
  .zen-icon--text-xxl {
    font-size: 87px; }
  .zen-icon--text-xl {
    font-size: 64px; }
  .zen-icon--text-lg {
    font-size: 42px; }
  .zen-icon--text-md {
    font-size: 37px; }
  .zen-icon--text-sm {
    font-size: 27px; }
  .zen-icon--text-xs {
    font-size: 24px; }
  .zen-icon--text-xxs {
    font-size: 17px; }

/*------------------------------------------------------------------------------
// Extend icon element & modifiers
//----------------------------------------------------------------------------*/
/*==============================================================================
// File:        _title.scss
// Package:     Joomla / Mr Zen
// Synopsis:    Shared title component - SASS
//============================================================================*/
.zen-title, .zen-heading-font-light .sppb-addon-title, .zen-heading-font-semibold .sppb-addon-title, .zen-heading-font-light-wrapped .sppb-addon-title, .zen-heading-font-semibold-wrapped .sppb-addon-title {
  padding: 0;
  /*==============================================================================
// Modifiers
//============================================================================*/
  /*------------------------------------------------------------------------------
	// Font Family Changes
	//----------------------------------------------------------------------------*/ }
  .zen-title strong, .zen-heading-font-light .sppb-addon-title strong, .zen-heading-font-semibold .sppb-addon-title strong, .zen-heading-font-light-wrapped .sppb-addon-title strong, .zen-heading-font-semibold-wrapped .sppb-addon-title strong,
  .zen-title span, .zen-heading-font-light .sppb-addon-title span, .zen-heading-font-semibold .sppb-addon-title span, .zen-heading-font-light-wrapped .sppb-addon-title span, .zen-heading-font-semibold-wrapped .sppb-addon-title span,
  .zen-title b, .zen-heading-font-light .sppb-addon-title b, .zen-heading-font-semibold .sppb-addon-title b, .zen-heading-font-light-wrapped .sppb-addon-title b, .zen-heading-font-semibold-wrapped .sppb-addon-title b {
    margin-right: 5px;
    margin-left: 5px; }
  .zen-title--light, .zen-heading-light, .zen-heading-light .sppb-addon-title {
    text-shadow: 1px 1px 20px #262626;
    color: #FFF; }
  .zen-title--white {
    color: #FFF; }
  .zen-title--dark, .zen-heading-dark, .zen-heading-dark .sppb-addon-title {
    color: #23383C; }
  .zen-title--primary, .zen-heading-primary, .zen-heading-primary .sppb-addon-title {
    color: #0095CC; }
  .zen-title--secondary, .zen-heading-secondary, .zen-heading-secondary .sppb-addon-title {
    color: #EE1656; }
  .zen-title--hr-primary {
    position: relative;
    margin: 0 0 35px;
    border-bottom: 5px solid #0095CC;
    margin: 15px auto;
    width: 20%;
    bottom: 0;
    right: 0;
    left: 0; }
    .zen-title--hr-primary:after {
      content: "";
      display: block;
      position: absolute;
      border-bottom: 5px solid #0095CC;
      margin: 15px auto;
      width: 20%;
      bottom: 0;
      right: 0;
      left: 0; }
  .zen-title--category {
    font-size: 18px;
    border-bottom: 1px solid #0095CC;
    padding: 0 0 10px;
    margin: 0 0 20px;
    font-weight: bold;
    color: #0095CC; }
    @media (min-width: 300px) {
      .zen-title--category {
        font-size: calc(18px + 3 * (100vw - 300px) / 1300); } }
    @media (min-width: 1600px) {
      .zen-title--category {
        font-size: 21px; } }
  .zen-title--booking {
    letter-spacing: normal;
    margin: 10px 0 5px;
    text-transform: none;
    font-style: italic;
    color: #23383C; }
  .zen-title--wrap strong, .zen-heading-font-light-wrapped .sppb-addon-title strong, .zen-heading-font-semibold-wrapped .sppb-addon-title strong,
  .zen-title--wrap span, .zen-heading-font-light-wrapped .sppb-addon-title span, .zen-heading-font-semibold-wrapped .sppb-addon-title span,
  .zen-title--wrap b, .zen-heading-font-light-wrapped .sppb-addon-title b, .zen-heading-font-semibold-wrapped .sppb-addon-title b {
    margin-bottom: 15px;
    margin-top: 15px;
    display: block; }
  .zen-title--hero {
    font-size: 22px;
    line-height: 1.75em; }
    @media (min-width: 300px) {
      .zen-title--hero {
        font-size: calc(22px + 10 * (100vw - 300px) / 1300); } }
    @media (min-width: 1600px) {
      .zen-title--hero {
        font-size: 32px; } }
  .zen-title--font-light, .zen-heading-font-light .sppb-addon-title, .zen-heading-font-light-wrapped .sppb-addon-title {
    font-family: "IBM Plex Sans", Helvetica, Arial, sans-serif;
    text-transform: uppercase; }
    .zen-title--font-light:where(h2), .zen-heading-font-light .sppb-addon-title:where(h2), .zen-heading-font-light-wrapped .sppb-addon-title:where(h2) {
      font-size: 28px; }
      @media (min-width: 300px) {
        .zen-title--font-light:where(h2), .zen-heading-font-light .sppb-addon-title:where(h2), .zen-heading-font-light-wrapped .sppb-addon-title:where(h2) {
          font-size: calc(28px + 2 * (100vw - 300px) / 1300); } }
      @media (min-width: 1600px) {
        .zen-title--font-light:where(h2), .zen-heading-font-light .sppb-addon-title:where(h2), .zen-heading-font-light-wrapped .sppb-addon-title:where(h2) {
          font-size: 30px; } }
    .zen-title--font-light:where(h3):where(.zen-search__title), .zen-heading-font-light .sppb-addon-title:where(h3):where(.zen-search__title), .zen-heading-font-light-wrapped .sppb-addon-title:where(h3):where(.zen-search__title) {
      font-size: 24px; }
      @media (min-width: 300px) {
        .zen-title--font-light:where(h3):where(.zen-search__title), .zen-heading-font-light .sppb-addon-title:where(h3):where(.zen-search__title), .zen-heading-font-light-wrapped .sppb-addon-title:where(h3):where(.zen-search__title) {
          font-size: calc(24px + 2 * (100vw - 300px) / 1300); } }
      @media (min-width: 1600px) {
        .zen-title--font-light:where(h3):where(.zen-search__title), .zen-heading-font-light .sppb-addon-title:where(h3):where(.zen-search__title), .zen-heading-font-light-wrapped .sppb-addon-title:where(h3):where(.zen-search__title) {
          font-size: 26px; } }
    .zen-title--font-light:where(h4), .zen-heading-font-light .sppb-addon-title:where(h4), .zen-heading-font-light-wrapped .sppb-addon-title:where(h4) {
      font-size: 20px; }
      @media (min-width: 300px) {
        .zen-title--font-light:where(h4), .zen-heading-font-light .sppb-addon-title:where(h4), .zen-heading-font-light-wrapped .sppb-addon-title:where(h4) {
          font-size: calc(20px + 4 * (100vw - 300px) / 1300); } }
      @media (min-width: 1600px) {
        .zen-title--font-light:where(h4), .zen-heading-font-light .sppb-addon-title:where(h4), .zen-heading-font-light-wrapped .sppb-addon-title:where(h4) {
          font-size: 24px; } }
    .zen-title--font-light:where(h5), .zen-heading-font-light .sppb-addon-title:where(h5), .zen-heading-font-light-wrapped .sppb-addon-title:where(h5) {
      font-size: 18px; }
      @media (min-width: 300px) {
        .zen-title--font-light:where(h5), .zen-heading-font-light .sppb-addon-title:where(h5), .zen-heading-font-light-wrapped .sppb-addon-title:where(h5) {
          font-size: calc(18px + 2 * (100vw - 300px) / 1300); } }
      @media (min-width: 1600px) {
        .zen-title--font-light:where(h5), .zen-heading-font-light .sppb-addon-title:where(h5), .zen-heading-font-light-wrapped .sppb-addon-title:where(h5) {
          font-size: 20px; } }
    .zen-title--font-light:where(h6), .zen-heading-font-light .sppb-addon-title:where(h6), .zen-heading-font-light-wrapped .sppb-addon-title:where(h6) {
      font-size: 16px; }
      @media (min-width: 300px) {
        .zen-title--font-light:where(h6), .zen-heading-font-light .sppb-addon-title:where(h6), .zen-heading-font-light-wrapped .sppb-addon-title:where(h6) {
          font-size: calc(16px + 2 * (100vw - 300px) / 1300); } }
      @media (min-width: 1600px) {
        .zen-title--font-light:where(h6), .zen-heading-font-light .sppb-addon-title:where(h6), .zen-heading-font-light-wrapped .sppb-addon-title:where(h6) {
          font-size: 18px; } }
  .zen-title--font-semibold, .zen-heading-font-semibold .sppb-addon-title, .zen-heading-font-semibold-wrapped .sppb-addon-title {
    font-family: "IBM Plex Sans SemiBold", Helvetica, Arial, sans-serif;
    text-transform: uppercase; }
    .zen-title--font-semibold:where(h2), .zen-heading-font-semibold .sppb-addon-title:where(h2), .zen-heading-font-semibold-wrapped .sppb-addon-title:where(h2) {
      font-size: 28px; }
      @media (min-width: 300px) {
        .zen-title--font-semibold:where(h2), .zen-heading-font-semibold .sppb-addon-title:where(h2), .zen-heading-font-semibold-wrapped .sppb-addon-title:where(h2) {
          font-size: calc(28px + 2 * (100vw - 300px) / 1300); } }
      @media (min-width: 1600px) {
        .zen-title--font-semibold:where(h2), .zen-heading-font-semibold .sppb-addon-title:where(h2), .zen-heading-font-semibold-wrapped .sppb-addon-title:where(h2) {
          font-size: 30px; } }
    .zen-title--font-semibold:where(h3):where(.zen-search__title), .zen-heading-font-semibold .sppb-addon-title:where(h3):where(.zen-search__title), .zen-heading-font-semibold-wrapped .sppb-addon-title:where(h3):where(.zen-search__title) {
      font-size: 24px; }
      @media (min-width: 300px) {
        .zen-title--font-semibold:where(h3):where(.zen-search__title), .zen-heading-font-semibold .sppb-addon-title:where(h3):where(.zen-search__title), .zen-heading-font-semibold-wrapped .sppb-addon-title:where(h3):where(.zen-search__title) {
          font-size: calc(24px + 2 * (100vw - 300px) / 1300); } }
      @media (min-width: 1600px) {
        .zen-title--font-semibold:where(h3):where(.zen-search__title), .zen-heading-font-semibold .sppb-addon-title:where(h3):where(.zen-search__title), .zen-heading-font-semibold-wrapped .sppb-addon-title:where(h3):where(.zen-search__title) {
          font-size: 26px; } }
    .zen-title--font-semibold:where(h4), .zen-heading-font-semibold .sppb-addon-title:where(h4), .zen-heading-font-semibold-wrapped .sppb-addon-title:where(h4) {
      font-size: 20px; }
      @media (min-width: 300px) {
        .zen-title--font-semibold:where(h4), .zen-heading-font-semibold .sppb-addon-title:where(h4), .zen-heading-font-semibold-wrapped .sppb-addon-title:where(h4) {
          font-size: calc(20px + 4 * (100vw - 300px) / 1300); } }
      @media (min-width: 1600px) {
        .zen-title--font-semibold:where(h4), .zen-heading-font-semibold .sppb-addon-title:where(h4), .zen-heading-font-semibold-wrapped .sppb-addon-title:where(h4) {
          font-size: 24px; } }
    .zen-title--font-semibold:where(h5), .zen-heading-font-semibold .sppb-addon-title:where(h5), .zen-heading-font-semibold-wrapped .sppb-addon-title:where(h5) {
      font-size: 18px; }
      @media (min-width: 300px) {
        .zen-title--font-semibold:where(h5), .zen-heading-font-semibold .sppb-addon-title:where(h5), .zen-heading-font-semibold-wrapped .sppb-addon-title:where(h5) {
          font-size: calc(18px + 2 * (100vw - 300px) / 1300); } }
      @media (min-width: 1600px) {
        .zen-title--font-semibold:where(h5), .zen-heading-font-semibold .sppb-addon-title:where(h5), .zen-heading-font-semibold-wrapped .sppb-addon-title:where(h5) {
          font-size: 20px; } }
    .zen-title--font-semibold:where(h6), .zen-heading-font-semibold .sppb-addon-title:where(h6), .zen-heading-font-semibold-wrapped .sppb-addon-title:where(h6) {
      font-size: 16px; }
      @media (min-width: 300px) {
        .zen-title--font-semibold:where(h6), .zen-heading-font-semibold .sppb-addon-title:where(h6), .zen-heading-font-semibold-wrapped .sppb-addon-title:where(h6) {
          font-size: calc(16px + 2 * (100vw - 300px) / 1300); } }
      @media (min-width: 1600px) {
        .zen-title--font-semibold:where(h6), .zen-heading-font-semibold .sppb-addon-title:where(h6), .zen-heading-font-semibold-wrapped .sppb-addon-title:where(h6) {
          font-size: 18px; } }

/*------------------------------------------------------------------------------
// Extend title element & modifiers
//----------------------------------------------------------------------------*/
/*==============================================================================
// File:        _gallery.scss
// Package:     Joomla / Mr Zen
// Synopsis:    Shared gallery component - SASS
//============================================================================*/
.zen-gallery {
  border-bottom: 1px solid #23383C;
  margin: 0 auto 30px;
  padding: 0 0 20px;
  max-width: 750px;
  /*==============================================================================
// Modifiers
//============================================================================*/ }
  .zen-gallery--large {
    min-height: 25vh;
    max-width: 100%;
    margin: 0 auto;
    border: none;
    padding: 0; }

/*==============================================================================
// File:        _select.scss
// Package:     Joomla / Mr Zen
// Synopsis:    Shared select component - SASS
//============================================================================*/
.zen-select, .zen-content-search__filter-ordering select, .zen-content-search__result-select select {
  margin: 0;
  /*==============================================================================
// Elements
//============================================================================*/
  /*==============================================================================
// Modifiers
//============================================================================*/ }
  .zen-select__span, .selectlist span, .zen-content-search__filter-ordering select {
    max-width: 100%;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    padding: 20px 55px 20px 15px;
    border: 1px solid #C6C6C6;
    text-transform: uppercase;
    background: #FFF;
    color: #8F8F8F;
    display: inline-block;
    text-rendering: auto;
    line-height: 1.1em;
    font-size: inherit;
    position: relative;
    text-align: left;
    min-width: 195px;
    width: 100%;
    z-index: 1; }
    .zen-select__span:after, .selectlist span:after, .zen-content-search__filter-ordering select:after {
      border-left: 1px solid #C6C6C6;
      background: #F2F2F2;
      position: absolute;
      margin-left: 10px;
      content: "\e81d";
      color: #0095CC;
      font-size: 20px;
      padding: 18px;
      right: 0;
      top: 0; }
    .zen-select__span:focus, .selectlist span:focus, .zen-content-search__filter-ordering select:focus {
      border-color: #0095CC;
      box-shadow: none;
      outline: none; }
    .zen-select__span.focus, .selectlist span.focus, .zen-content-search__filter-ordering select.focus {
      border-color: #0095CC;
      box-shadow: none;
      outline: none; }
  .zen-select--default, .selectlist {
    font-size: 15px;
    position: relative; }

/*------------------------------------------------------------------------------
// Extend menu element & modifiers
//----------------------------------------------------------------------------*/
/*------------------------------------------------------------------------------
// Default AngularJS select styling
//----------------------------------------------------------------------------*/
.selectlist:hover {
  cursor: pointer; }

.selectlist select {
  position: absolute;
  height: 58px;
  border: none;
  width: 100%;
  opacity: 0;
  z-index: 2;
  left: 0;
  top: 0; }
  .selectlist select:read-only, .selectlist select:disabled {
    background: none;
    opacity: 0; }

.zen-styled-select {
  border-color: #C6C6C6;
  background: #FFF;
  padding: 5px;
  opacity: 0; }

/*==============================================================================
// File:        _button.scss
// Package:     Joomla / Mr Zen
// Synopsis:    Shared button component - SASS
//============================================================================*/
.zen-btn, .btn, .btn.btn-primary, a.zen-btn:not([href]):not([tabindex]), .rsform-submit-button, .rsform-calendar-button, .rsform-calendar-button.btn.btn-secondary, .zen-btn--hero .sp-slider-btn-text, .sppb-btn, .sppb-btn.sppb-btn-default, .sppb-sp-slider-button .sp-slider-btn-text, .sppb-readmore {
  font-size: 13px;
  padding-right: 30px;
  padding-left: 30px;
  -webkit-transition: all 0.5s ease 0.1s;
  -moz-transition: all 0.5s ease 0.1s;
  -ms-transition: all 0.5s ease 0.1s;
  -o-transition: all 0.5s ease 0.1s;
  transition: all 0.5s ease 0.1s;
  font-family: "IBM Plex Sans SemiBold", Helvetica, Arial, sans-serif;
  border: 4px solid #0095CC;
  text-transform: uppercase;
  padding-bottom: 13px;
  z-index: 5;
  padding-top: 13px;
  text-decoration: none;
  display: inline-block;
  background: #0095CC;
  position: relative;
  color: #FFF;
  border-radius: 0;
  line-height: 1em;
  min-height: 50px;
  /*==============================================================================
// Modifiers
//============================================================================*/
  /*------------------------------------------------------------------------------
	// Accordion Buttons
	//----------------------------------------------------------------------------*/
  /*------------------------------------------------------------------------------
	// Mobile Menu Accordions
	//----------------------------------------------------------------------------*/
  /*------------------------------------------------------------------------------
	// Filter Panel
	//----------------------------------------------------------------------------*/
  /*------------------------------------------------------------------------------
	// Font Size Changes
	//----------------------------------------------------------------------------*/ }
  @media (min-width: 300px) {
    .zen-btn, .btn, .btn.btn-primary, a.zen-btn:not([href]):not([tabindex]), .rsform-submit-button, .rsform-calendar-button, .rsform-calendar-button.btn.btn-secondary, .zen-btn--hero .sp-slider-btn-text, .sppb-btn, .sppb-btn.sppb-btn-default, .sppb-sp-slider-button .sp-slider-btn-text, .sppb-readmore {
      font-size: calc(13px + 2 * (100vw - 300px) / 1300); } }
  @media (min-width: 1600px) {
    .zen-btn, .btn, .btn.btn-primary, a.zen-btn:not([href]):not([tabindex]), .rsform-submit-button, .rsform-calendar-button, .rsform-calendar-button.btn.btn-secondary, .zen-btn--hero .sp-slider-btn-text, .sppb-btn, .sppb-btn.sppb-btn-default, .sppb-sp-slider-button .sp-slider-btn-text, .sppb-readmore {
      font-size: 15px; } }
  @media (min-width: 300px) {
    .zen-btn, .btn, .btn.btn-primary, a.zen-btn:not([href]):not([tabindex]), .rsform-submit-button, .rsform-calendar-button, .rsform-calendar-button.btn.btn-secondary, .zen-btn--hero .sp-slider-btn-text, .sppb-btn, .sppb-btn.sppb-btn-default, .sppb-sp-slider-button .sp-slider-btn-text, .sppb-readmore {
      padding-right: calc(20px + 10 * (100vw - 300px) / 1300); } }
  @media (min-width: 1600px) {
    .zen-btn, .btn, .btn.btn-primary, a.zen-btn:not([href]):not([tabindex]), .rsform-submit-button, .rsform-calendar-button, .rsform-calendar-button.btn.btn-secondary, .zen-btn--hero .sp-slider-btn-text, .sppb-btn, .sppb-btn.sppb-btn-default, .sppb-sp-slider-button .sp-slider-btn-text, .sppb-readmore {
      padding-right: 30px; } }
  @media (min-width: 300px) {
    .zen-btn, .btn, .btn.btn-primary, a.zen-btn:not([href]):not([tabindex]), .rsform-submit-button, .rsform-calendar-button, .rsform-calendar-button.btn.btn-secondary, .zen-btn--hero .sp-slider-btn-text, .sppb-btn, .sppb-btn.sppb-btn-default, .sppb-sp-slider-button .sp-slider-btn-text, .sppb-readmore {
      padding-left: calc(20px + 10 * (100vw - 300px) / 1300); } }
  @media (min-width: 1600px) {
    .zen-btn, .btn, .btn.btn-primary, a.zen-btn:not([href]):not([tabindex]), .rsform-submit-button, .rsform-calendar-button, .rsform-calendar-button.btn.btn-secondary, .zen-btn--hero .sp-slider-btn-text, .sppb-btn, .sppb-btn.sppb-btn-default, .sppb-sp-slider-button .sp-slider-btn-text, .sppb-readmore {
      padding-left: 30px; } }
  .zen-btn:active, .btn:active, a.zen-btn:active:not([href]):not([tabindex]), .rsform-submit-button:active, .rsform-calendar-button:active, .rsform-calendar-button.btn.btn-secondary:active, .zen-btn--hero .sp-slider-btn-text:active, .sppb-btn:active, .sppb-sp-slider-button .sp-slider-btn-text:active, .sppb-readmore:active, .zen-btn:hover, .btn:hover, a.zen-btn:hover:not([href]):not([tabindex]), .rsform-submit-button:hover, .rsform-calendar-button:hover, .rsform-calendar-button.btn.btn-secondary:hover, .zen-btn--hero .sp-slider-btn-text:hover, .sppb-btn:hover, .sppb-sp-slider-button .sp-slider-btn-text:hover, .sppb-readmore:hover, .zen-btn:focus, .btn:focus, a.zen-btn:focus:not([href]):not([tabindex]), .rsform-submit-button:focus, .rsform-calendar-button:focus, .rsform-calendar-button.btn.btn-secondary:focus, .zen-btn--hero .sp-slider-btn-text:focus, .sppb-btn:focus, .sppb-sp-slider-button .sp-slider-btn-text:focus, .sppb-readmore:focus {
    border-color: #048abb;
    background: #048abb;
    text-decoration: none;
    color: #FFF;
    cursor: pointer; }
  .zen-btn:disabled, .btn:disabled, a.zen-btn:disabled:not([href]):not([tabindex]), .rsform-submit-button:disabled, .rsform-calendar-button:disabled, .rsform-calendar-button.btn.btn-secondary:disabled, .zen-btn--hero .sp-slider-btn-text:disabled, .sppb-btn:disabled, .sppb-sp-slider-button .sp-slider-btn-text:disabled, .sppb-readmore:disabled {
    cursor: not-allowed;
    background: #F2F2F2;
    color: #23383C;
    opacity: .6; }
  .zen-btn--default, .sppb-btn.sppb-btn-dark {
    border-color: #23383C;
    background: #23383C; }
    .zen-btn--default:hover, .sppb-btn.sppb-btn-dark:hover, .zen-btn--default:focus, .sppb-btn.sppb-btn-dark:focus {
      border-color: #c6194c;
      background: #c6194c; }
  .zen-btn--yellow {
    border-color: #FFF;
    background: #FFC105;
    color: #262626; }
    .zen-btn--yellow:hover, .zen-btn--yellow:focus {
      background: #262626;
      border: 4px solid #262626;
      color: #FFF; }
  .zen-btn--medium, .sppb-btn.sppb-btn-success {
    border-color: #8F8F8F;
    background: #8F8F8F; }
    .zen-btn--medium:hover, .sppb-btn.sppb-btn-success:hover, .zen-btn--medium:focus, .sppb-btn.sppb-btn-success:focus {
      border-color: #23383C;
      background: #23383C; }
  .zen-btn--light {
    border-color: #C6C6C6;
    background: #C6C6C6; }
    .zen-btn--light:hover, .zen-btn--light:focus {
      border-color: #8F8F8F;
      background: #8F8F8F; }
  .zen-btn--lighter, .sppb-readmore {
    border-color: #FFF;
    background: #FFF;
    color: #23383C; }
    .zen-btn--lighter:hover, .sppb-readmore:hover, .zen-btn--lighter:focus, .sppb-readmore:focus {
      border-color: #FFF;
      background: #FFF;
      color: #EE1656; }
  .zen-btn--secondary, .zen-btn--hero .sp-slider-btn-text, .sppb-btn.sppb-btn-secondary, .sppb-btn.sppb-btn-danger {
    border-color: #EE1656;
    background: #EE1656; }
    .zen-btn--secondary:hover, .zen-btn--hero .sp-slider-btn-text:hover, .sppb-btn.sppb-btn-secondary:hover, .sppb-btn.sppb-btn-danger:hover, .zen-btn--secondary:focus, .zen-btn--hero .sp-slider-btn-text:focus, .sppb-btn.sppb-btn-secondary:focus, .sppb-btn.sppb-btn-danger:focus {
      border-color: #da1851;
      background: #da1851; }
  .zen-btn--invert, .btn.btn-secondary {
    border-color: #048abb;
    background: #048abb; }
    .zen-btn--invert:hover, .btn.btn-secondary:hover, .zen-btn--invert:focus, .btn.btn-secondary:focus {
      border-color: #0095CC;
      background: #0095CC; }
  .zen-btn--outlined, .btn.btn-success, a.zen-btn--outlined:not([href]):not([tabindex]), .sppb-btn.sppb-btn-primary {
    background: #FFF;
    border-color: #0095CC;
    color: #0095CC; }
    .zen-btn--outlined:hover, .btn.btn-success:hover, a.zen-btn--outlined:hover:not([href]):not([tabindex]), .sppb-btn.sppb-btn-primary:hover, .zen-btn--outlined:focus, .btn.btn-success:focus, a.zen-btn--outlined:focus:not([href]):not([tabindex]), .sppb-btn.sppb-btn-primary:focus {
      border-color: #048abb;
      background: #048abb;
      color: #FFF; }
  .zen-btn--outlined-default, .sppb-btn.sppb-btn-info {
    background: #FFF;
    border-color: #23383C;
    color: #23383C; }
    .zen-btn--outlined-default:hover, .sppb-btn.sppb-btn-info:hover, .zen-btn--outlined-default:focus, .sppb-btn.sppb-btn-info:focus {
      border-color: #23383C;
      background: #23383C;
      color: #FFF; }
  .zen-btn--outlined-medium, .sppb-btn.sppb-btn-link {
    border-color: #8F8F8F;
    background: #FFF;
    color: #8F8F8F; }
    .zen-btn--outlined-medium:hover, .sppb-btn.sppb-btn-link:hover, .zen-btn--outlined-medium:focus, .sppb-btn.sppb-btn-link:focus {
      border-color: #8F8F8F;
      background: #8F8F8F;
      color: #FFF; }
  .zen-btn--outlined-dark {
    border-color: #1E3339;
    background: #FFF;
    color: #1E3339; }
    .zen-btn--outlined-dark:hover, .zen-btn--outlined-dark:focus {
      border-color: #262626;
      background: #262626;
      color: #FFF; }
  .zen-btn--outlined-light {
    border-color: #C6C6C6;
    background: #FFF;
    color: #C6C6C6; }
    .zen-btn--outlined-light:hover, .zen-btn--outlined-light:focus {
      border-color: #C6C6C6;
      background: #C6C6C6;
      color: #FFF; }
  .zen-btn--outlined-secondary, .sppb-btn.sppb-btn-warning {
    border-color: #EE1656;
    background: #FFF;
    color: #EE1656; }
    .zen-btn--outlined-secondary:hover, .sppb-btn.sppb-btn-warning:hover, .zen-btn--outlined-secondary:focus, .sppb-btn.sppb-btn-warning:focus {
      border-color: #EE1656;
      background: #EE1656;
      color: #FFF; }
  .zen-btn--disabled {
    border-color: #EE1656;
    background: #EE1656;
    cursor: not-allowed; }
    .zen-btn--disabled:hover, .zen-btn--disabled:focus {
      border-color: #da1851;
      background: #da1851; }
  .zen-btn--icon-submit {
    border: none;
    /* Reset */
    border-left: 10px solid #FFF;
    font-size: 24px;
    padding: 10px 20px;
    background: #0095CC;
    position: absolute;
    color: #FFF;
    line-height: 1em;
    border-radius: 0;
    right: 0;
    top: 0; }
    .zen-btn--icon-submit:hover, .zen-btn--icon-submit:focus {
      border-color: #FFF;
      outline: none; }
    .zen-btn--icon-submit:before {
      content: "";
      display: block;
      position: absolute;
      background: #8F8F8F;
      height: 100%;
      left: -10px;
      width: 1px;
      top: 0; }
  .zen-btn--max-size {
    max-height: 58px; }
  .zen-btn--full-size, .rsform-submit-button {
    width: 100%; }
  .zen-btn--extra-pad-xl {
    padding: 19px 70px; }
  .zen-btn--extra-pad-lg {
    padding-right: 60px;
    padding-left: 60px; }
  .zen-btn--extra-pad-md {
    padding-right: 50px;
    padding-left: 50px; }
  .zen-btn--extra-pad-sm {
    padding-right: 40px;
    padding-left: 40px; }
  .zen-btn--scroll {
    border: 1px solid #048abb;
    background: #0095CC;
    color: #FFF;
    font-size: 30px;
    padding: 7px;
    display: none;
    width: 45px; }
    .zen-btn--scroll:hover, .zen-btn--scroll:focus {
      background: #048abb;
      color: #FFF; }
    .zen-btn--scroll.show-btn {
      display: -webkit-flex;
      display: flex; }
    .zen-btn--scroll :before {
      vertical-align: top; }
  .zen-btn--collapsing .before-open {
    display: block; }
  .zen-btn--collapsing .after-open {
    display: none; }
  .zen-btn--collapsing[aria-expanded="true"] .before-open {
    display: none; }
  .zen-btn--collapsing[aria-expanded="true"] .after-open {
    display: block; }
  .zen-btn--accordion-main, .zen-btn--accordion-sub, .zen-accordion__btn-sub, a.zen-btn--accordion-sub:not([href]):not([tabindex]), .zen-btn--accordion-mobile-menu, .zen-btn--accordion-mobile-submenu, .zen-accordion__btn-main, a.zen-btn--accordion-main:not([href]):not([tabindex]) {
    font-size: 18px;
    border: none;
    /* Override */
    font-family: "IBM Plex Sans SemiBold", Helvetica, Arial, sans-serif;
    border-bottom: 1px solid #C6C6C6;
    background: #FFF;
    padding: 20px 30px;
    line-height: 1.5em;
    text-align: left;
    border-radius: 0;
    color: #23383C;
    margin: 0; }
    @media (min-width: 300px) {
      .zen-btn--accordion-main, .zen-btn--accordion-sub, .zen-accordion__btn-sub, a.zen-btn--accordion-sub:not([href]):not([tabindex]), .zen-btn--accordion-mobile-menu, .zen-btn--accordion-mobile-submenu, .zen-accordion__btn-main, a.zen-btn--accordion-main:not([href]):not([tabindex]) {
        font-size: calc(18px + 4 * (100vw - 300px) / 1300); } }
    @media (min-width: 1600px) {
      .zen-btn--accordion-main, .zen-btn--accordion-sub, .zen-accordion__btn-sub, a.zen-btn--accordion-sub:not([href]):not([tabindex]), .zen-btn--accordion-mobile-menu, .zen-btn--accordion-mobile-submenu, .zen-accordion__btn-main, a.zen-btn--accordion-main:not([href]):not([tabindex]) {
        font-size: 22px; } }
    .zen-btn--accordion-main:hover, .zen-btn--accordion-sub:hover, .zen-accordion__btn-sub:hover, a.zen-btn--accordion-sub:hover:not([href]):not([tabindex]), .zen-btn--accordion-mobile-menu:hover, .zen-btn--accordion-mobile-submenu:hover, .zen-accordion__btn-main:hover, a.zen-btn--accordion-main:hover:not([href]):not([tabindex]), .zen-btn--accordion-main:focus, .zen-btn--accordion-sub:focus, .zen-accordion__btn-sub:focus, a.zen-btn--accordion-sub:focus:not([href]):not([tabindex]), .zen-btn--accordion-mobile-menu:focus, .zen-btn--accordion-mobile-submenu:focus, .zen-accordion__btn-main:focus, a.zen-btn--accordion-main:focus:not([href]):not([tabindex]) {
      color: #1aa0d1;
      border-color: #C6C6C6;
      background: #FFF;
      cursor: pointer;
      outline: none; }
    .zen-btn--accordion-main:after, .zen-btn--accordion-sub:after, .zen-accordion__btn-sub:after, a.zen-btn--accordion-sub:not([tabindex]):not([href]):after, .zen-btn--accordion-mobile-menu:after, .zen-btn--accordion-mobile-submenu:after, .zen-accordion__btn-main:after, a.zen-btn--accordion-main:not([tabindex]):not([href]):after {
      -webkit-transition: transform 0.3s ease-in-out 0s;
      -moz-transition: transform 0.3s ease-in-out 0s;
      -ms-transition: transform 0.3s ease-in-out 0s;
      -o-transition: transform 0.3s ease-in-out 0s;
      transition: transform 0.3s ease-in-out 0s;
      transform-origin: center;
      content: "\e81d";
      font-size: 30px;
      float: right; }
    .zen-btn--accordion-main[aria-expanded="true"], [aria-expanded="true"].zen-btn--accordion-sub, [aria-expanded="true"].zen-accordion__btn-sub, a[aria-expanded="true"].zen-btn--accordion-sub:not([href]):not([tabindex]), [aria-expanded="true"].zen-btn--accordion-mobile-menu, [aria-expanded="true"].zen-btn--accordion-mobile-submenu, [aria-expanded="true"].zen-accordion__btn-main, a[aria-expanded="true"].zen-btn--accordion-main:not([href]):not([tabindex]) {
      border-color: #0095CC;
      background: #0095CC;
      color: #FFF; }
      .zen-btn--accordion-main[aria-expanded="true"]:after, [aria-expanded="true"].zen-btn--accordion-sub:after, [aria-expanded="true"].zen-accordion__btn-sub:after, a[aria-expanded="true"].zen-btn--accordion-sub:not([tabindex]):not([href]):after, [aria-expanded="true"].zen-btn--accordion-mobile-menu:after, [aria-expanded="true"].zen-btn--accordion-mobile-submenu:after, [aria-expanded="true"].zen-accordion__btn-main:after, a[aria-expanded="true"].zen-btn--accordion-main:not([tabindex]):not([href]):after {
        transform: rotate(180deg); }
    .zen-btn--accordion-main--first {
      border-top: 1px solid #C6C6C6; }
  .zen-btn--accordion-sub, .zen-accordion__btn-sub, a.zen-btn--accordion-sub:not([href]):not([tabindex]) {
    padding: 20px 20px 20px 0; }
    .zen-btn--accordion-sub:after, .zen-accordion__btn-sub:after, a.zen-btn--accordion-sub:not([tabindex]):not([href]):after {
      display: inline-block;
      content: "\e859";
      margin: 0 0 0 3%;
      float: none; }
    .zen-btn--accordion-sub[aria-expanded="true"], [aria-expanded="true"].zen-accordion__btn-sub, a[aria-expanded="true"].zen-btn--accordion-sub:not([href]):not([tabindex]) {
      background: #FFF;
      border: none;
      color: #23383C; }
  .zen-btn--accordion-mobile-menu {
    font-size: 15px;
    padding: 15px 10px; }
    .zen-btn--accordion-mobile-menu:after {
      font-size: 24px;
      color: #0095CC; }
    .zen-btn--accordion-mobile-menu[aria-expanded="true"] {
      background: #FFF;
      border: none;
      color: #23383C; }
  .zen-btn--accordion-mobile-submenu {
    border: none;
    /* Reset */
    border-bottom: 1px solid #C6C6C6;
    font-size: 15px;
    padding: 15px 10px; }
    .zen-btn--accordion-mobile-submenu:after {
      font-size: 24px;
      color: #C6C6C6;
      content: "\e859"; }
    .zen-btn--accordion-mobile-submenu[aria-expanded="true"] {
      border-color: #C6C6C6;
      background: #FFF;
      color: #23383C; }
  .zen-btn--navigate {
    border: none;
    /* Override */
    font-family: "IBM Plex Sans SemiBold", Helvetica, Arial, sans-serif;
    border-bottom: 1px solid #C6C6C6;
    justify-content: space-between;
    -webkit-align-items: center;
    color: #394c50;
    text-transform: uppercase;
    background: #FFF;
    display: -webkit-flex;
    align-items: center;
    font-weight: normal;
    text-align: unset;
    display: flex; }
    .zen-btn--navigate:hover, .zen-btn--navigate:focus {
      color: #1aa0d1;
      background: #FFF;
      outline: none; }
  .zen-btn--text-xl {
    font-size: 18px; }
    @media (min-width: 300px) {
      .zen-btn--text-xl {
        font-size: calc(18px + 2 * (100vw - 300px) / 1300); } }
    @media (min-width: 1600px) {
      .zen-btn--text-xl {
        font-size: 20px; } }
  .zen-btn--text-lg {
    font-size: 15px; }
    @media (min-width: 300px) {
      .zen-btn--text-lg {
        font-size: calc(15px + 3 * (100vw - 300px) / 1300); } }
    @media (min-width: 1600px) {
      .zen-btn--text-lg {
        font-size: 18px; } }
  .zen-btn--text-md {
    font-size: 13px; }
    @media (min-width: 300px) {
      .zen-btn--text-md {
        font-size: calc(13px + 3 * (100vw - 300px) / 1300); } }
    @media (min-width: 1600px) {
      .zen-btn--text-md {
        font-size: 16px; } }
  .zen-btn--text-sm {
    font-size: 11px; }
    @media (min-width: 300px) {
      .zen-btn--text-sm {
        font-size: calc(11px + 2 * (100vw - 300px) / 1300); } }
    @media (min-width: 1600px) {
      .zen-btn--text-sm {
        font-size: 13px; } }
  .zen-btn--text-xs {
    font-size: 10px; }
    @media (min-width: 300px) {
      .zen-btn--text-xs {
        font-size: calc(10px + 2 * (100vw - 300px) / 1300); } }
    @media (min-width: 1600px) {
      .zen-btn--text-xs {
        font-size: 12px; } }

/*------------------------------------------------------------------------------
// Extend button element & modifiers
//----------------------------------------------------------------------------*/
/*==============================================================================
// File:        _pagination.scss
// Package:     Joomla / Mr Zen
// Synopsis:    Shared pagination component - SASS
//============================================================================*/
.zen-pagination {
  padding: 0;
  /*==============================================================================
// Elements
//============================================================================*/ }
  .zen-pagination__item, .pagination .page-item {
    border: none;
    margin: 2px; }
    .zen-pagination__item.active a, .pagination .active.page-item a {
      color: #FFF !important;
      border-color: #0095CC;
      text-decoration: none;
      background: #0095CC; }

/*------------------------------------------------------------------------------
// Extend pagination element & modifiers
//----------------------------------------------------------------------------*/
/*==============================================================================
// File:        _accordion.scss
// Package:     Joomla / Mr Zen
// Synopsis:    Shared accordion component - SASS
//============================================================================*/
.zen-accordion {
  padding: 0;
  /*==============================================================================
// Elements
//============================================================================*/
  /*==============================================================================
// Modifiers
//============================================================================*/ }
  .zen-accordion__btn-sub {
    /* Modifiers */ }
    .zen-accordion__btn-sub--lg {
      font-size: 22px; }
      @media (min-width: 300px) {
        .zen-accordion__btn-sub--lg {
          font-size: calc(22px + 6 * (100vw - 300px) / 1300); } }
      @media (min-width: 1600px) {
        .zen-accordion__btn-sub--lg {
          font-size: 28px; } }
  .zen-accordion__content {
    margin: 0; }
    .zen-accordion__content.show {
      border-bottom: 1px solid #C6C6C6; }
  .zen-accordion__content-cell {
    padding: 25px 0; }
    @media (max-width: 767.98px) {
      .zen-accordion__content-cell {
        padding: 10px 0; } }
    .zen-accordion__content-cell span {
      display: block; }
      @media (max-width: 767.98px) {
        .zen-accordion__content-cell span {
          display: inline; } }
  .zen-accordion__timeline-marker {
    position: relative; }
    .zen-accordion__timeline-marker:before {
      content: "";
      display: block;
      position: absolute;
      border: 1px solid #C6C6C6;
      background: #FFF;
      z-index: 10;
      border-radius: 50%;
      height: 20px;
      width: 20px;
      left: -45px;
      top: 28px; }
  .zen-accordion--timeline {
    padding: 0 0 0 50px;
    position: relative;
    overflow: hidden; }
    .zen-accordion--timeline:after {
      content: "";
      display: block;
      position: absolute;
      background: #C6C6C6;
      height: 100%;
      width: 1px;
      left: 15px;
      top: 30px; }

/*==============================================================================
// File:        _facet.scss
// Package:     Joomla / Mr Zen
// Synopsis:    Shared facet component - SASS
//============================================================================*/
.zen-facet {
  border-top: 1px solid #C6C6C6;
  padding: 15px 0 20px;
  position: relative;
  /*==============================================================================
// Elements
//============================================================================*/ }
  .zen-facet .btn-collapse {
    padding: 0 5px; }
  .zen-facet + .facet {
    margin-top: 20px; }
  @media (max-width: 991.98px) {
    .zen-facet {
      font-size: 13px; } }
  .zen-facet__scroll-filters {
    display: block; }
    @media (max-width: 991.98px) {
      .zen-facet__scroll-filters {
        -webkit-transition: all 0.3s ease-in-out 0s;
        -moz-transition: all 0.3s ease-in-out 0s;
        -ms-transition: all 0.3s ease-in-out 0s;
        -o-transition: all 0.3s ease-in-out 0s;
        transition: all 0.3s ease-in-out 0s;
        background: #FFF;
        z-index: 30;
        position: fixed;
        overflow: unset;
        max-height: 85%;
        bottom: -1000px;
        width: 100%;
        z-index: 0;
        opacity: 0;
        margin: 0;
        left: 0; } }
    @media (min-width: 992px) {
      .zen-facet__scroll-filters {
        display: none; } }
  .zen-facet__main-filters {
    display: block;
    overflow: auto; }
    @media (max-width: 991.98px) {
      .zen-facet__main-filters {
        -webkit-transition: all 0.3s ease-in-out 0s;
        -moz-transition: all 0.3s ease-in-out 0s;
        -ms-transition: all 0.3s ease-in-out 0s;
        -o-transition: all 0.3s ease-in-out 0s;
        transition: all 0.3s ease-in-out 0s;
        background: #FFF;
        min-width: 220px;
        position: fixed;
        overflow: auto;
        max-width: 90%;
        left: -1000px;
        height: 100%;
        z-index: 0;
        opacity: 0;
        margin: 0;
        top: 0; } }
    .zen-facet__main-filters .zen-search__refine-footer {
      position: -webkit-sticky;
      position: sticky; }
  .zen-facet__body {
    border: 1px solid #C6C6C6;
    background: #FFF;
    padding: 20px 15px;
    z-index: 10;
    position: relative; }
    @media (max-width: 991.98px) {
      .zen-facet__body {
        position: relative;
        overflow-y: auto;
        overflow-x: hidden;
        min-height: 83vh;
        display: none;
        border: none;
        padding: 0; } }
  .zen-facet__filter-panel {
    padding: 0; }
  .zen-facet__filter-panel-item {
    -webkit-transition: all 0.3s ease-in-out 0s;
    -moz-transition: all 0.3s ease-in-out 0s;
    -ms-transition: all 0.3s ease-in-out 0s;
    -o-transition: all 0.3s ease-in-out 0s;
    transition: all 0.3s ease-in-out 0s;
    padding: 5px 15px;
    position: absolute;
    left: 1000px;
    opacity: 0; }
    .zen-facet__filter-panel-item.active {
      -webkit-transform: translateX(-1000px);
      -moz-transform: translateX(-1000px);
      -ms-transform: translateX(-1000px);
      -o-transform: translateX(-1000px);
      transform: translateX(-1000px);
      position: relative;
      opacity: 1; }
  @media (min-width: 992px) {
    .zen-facet__item {
      max-height: 350px;
      overflow: auto; } }
  .zen-facet__clear {
    position: absolute;
    bottom: 85px;
    right: 0; }
    .zen-facet__clear:hover, .zen-facet__clear:focus {
      text-decoration: underline;
      cursor: pointer; }
    @media (max-width: 991.98px) {
      /* .zen-facet__clear {
        bottom: auto;
        right: 15px;
        top: -35px; } */
        .zen-facet__clear:hover, .zen-facet__clear:focus {
          text-decoration: none; } }

/*------------------------------------------------------------------------------
// Extend menu element & modifiers
//----------------------------------------------------------------------------*/
/*------------------------------------------------------------------------------
// Mobile menu features
//----------------------------------------------------------------------------*/
@media (max-width: 991.98px) {
  .zen-filters-open {
    max-height: 90vh;
    overflow: hidden; }
    .zen-filters-open .zen-search__refine-header,
    .zen-filters-open .zen-search__refine-footer,
    .zen-filters-open .zen-facet__body,
    .zen-filters-open .zen-overlay {
      display: block;
      opacity: 1; }
    .zen-filters-open .zen-facet__scroll-filters {
      -webkit-transform: translateY(-1000px);
      -moz-transform: translateY(-1000px);
      -ms-transform: translateY(-1000px);
      -o-transform: translateY(-1000px);
      transform: translateY(-1000px);
      z-index: 30;
      padding: 0;
      opacity: 1; }
    .zen-filters-open .zen-facet__main-filters {
      -webkit-transform: translateX(1000px);
      -moz-transform: translateX(1000px);
      -ms-transform: translateX(1000px);
      -o-transform: translateX(1000px);
      transform: translateX(1000px);
      z-index: 30;
      padding: 0;
      opacity: 1; } }

/*==============================================================================
// File:        _input.scss
// Package:     Joomla / Mr Zen
// Synopsis:    Shared input component - SASS
//============================================================================*/
.zen-input, .form-control, .rsform-select-box,
.rsform-input-box, .sppb-form-group .sppb-form-control,
.sppb-form-control {
  border: 1px solid #8F8F8F;
  font-size: 15px;
  background: #FFF;
  border-radius: 0;
  transition: none;
  padding: 13px;
  height: auto; }
  .zen-input:focus, .form-control:focus, .rsform-select-box:focus,
  .rsform-input-box:focus,
  .sppb-form-control:focus {
    border-color: #0095CC;
    box-shadow: none;
    outline: none;
    color: #23383C; }
  .zen-input::-webkit-input-placeholder, .form-control::-webkit-input-placeholder, .rsform-select-box::-webkit-input-placeholder,
  .rsform-input-box::-webkit-input-placeholder, .sppb-form-group .sppb-form-control::-webkit-input-placeholder,
  .sppb-form-control::-webkit-input-placeholder {
    color: #8F8F8F; }
  .zen-input:-ms-input-placeholder, .form-control:-ms-input-placeholder, .rsform-select-box:-ms-input-placeholder,
  .rsform-input-box:-ms-input-placeholder,
  .sppb-form-control:-ms-input-placeholder {
    color: #8F8F8F; }
  .zen-input::placeholder, .form-control::placeholder, .rsform-select-box::placeholder,
  .rsform-input-box::placeholder, .sppb-form-group .sppb-form-control::placeholder,
  .sppb-form-control::placeholder {
    color: #8F8F8F; }

/*------------------------------------------------------------------------------
// Extend input element & modifiers
//----------------------------------------------------------------------------*/
/*==============================================================================
// File:        _form.scss
// Package:     Joomla / Mr Zen
// Synopsis:    Shared form component - SASS
//============================================================================*/
.zen-form {
  position: relative; }

/*==============================================================================
// File:        _text.scss
// Package:     Joomla / Mr Zen
// Synopsis:    Shared text component - SASS
//============================================================================*/
.zen-text, .zen-article__title, .sppb-article-title, .zen-article__tags, .sppb-article-meta {
  padding: 0;
  /*==============================================================================
// Modifiers
//============================================================================*/
  /*------------------------------------------------------------------------------
	// Font Size Changes
	//----------------------------------------------------------------------------*/
  /*------------------------------------------------------------------------------
	// Font Family Changes
	//----------------------------------------------------------------------------*/ }
  .zen-text--lead {
    font-size: 15px;
    font-family: "IBM Plex Sans SemiBold", Helvetica, Arial, sans-serif; }
    @media (min-width: 300px) {
      .zen-text--lead {
        font-size: calc(15px + 3 * (100vw - 300px) / 1300); } }
    @media (min-width: 1600px) {
      .zen-text--lead {
        font-size: 18px; } }
  .zen-text--subtitle {
    font-family: "IBM Plex Sans SemiBold", Helvetica, Arial, sans-serif;
    text-transform: uppercase;
    color: #8F8F8F; }
  .zen-text--default {
    color: #23383C; }
  .zen-text--default-bold {
    line-height: 18px;
    font-weight: bold;
    color: #23383C; }
  .zen-text--default-light {
    color: #C6C6C6; }
  .zen-text--primary {
    color: #0095CC; }
  .zen-text--secondary {
    color: #EE1656; }
  .zen-text--success {
    color: #069C1F; }
  .zen-text--alert {
    color: #CC8800; }
  .zen-text--light, .zen-article__title, .sppb-article-title, .zen-article__tags, .sppb-article-meta {
    color: #FFF; }
  .zen-text--light-bold {
    color: #FFF;
    font-weight: bold; }
  .zen-text--large {
    font-size: 14px;
    line-height: 24px; }
    @media (min-width: 300px) {
      .zen-text--large {
        font-size: calc(14px + 2 * (100vw - 300px) / 1300); } }
    @media (min-width: 1600px) {
      .zen-text--large {
        font-size: 16px; } }
  .zen-text--large-bold {
    font-size: 14px;
    line-height: 19px;
    font-weight: bold; }
    @media (min-width: 300px) {
      .zen-text--large-bold {
        font-size: calc(14px + 2 * (100vw - 300px) / 1300); } }
    @media (min-width: 1600px) {
      .zen-text--large-bold {
        font-size: 16px; } }
  .zen-text--availability {
    font-size: 16px;
    vertical-align: middle;
    display: inline-block;
    color: #EE1656;
    padding: 0;
    margin: 0; }
    .zen-text--availability.active {
      font-weight: bold;
      color: #0095CC; }
  .zen-text--author {
    font-size: 16px;
    line-height: 18px;
    color: #23383C; }
    @media (min-width: 300px) {
      .zen-text--author {
        font-size: calc(16px + 10 * (100vw - 300px) / 1300); } }
    @media (min-width: 1600px) {
      .zen-text--author {
        font-size: 26px; } }
  .zen-text--text-df {
    font-size: 14px; }
    @media (min-width: 300px) {
      .zen-text--text-df {
        font-size: calc(14px + 1 * (100vw - 300px) / 1300); } }
    @media (min-width: 1600px) {
      .zen-text--text-df {
        font-size: 15px; } }
  .zen-text--text-xl {
    font-size: 18px; }
    @media (min-width: 300px) {
      .zen-text--text-xl {
        font-size: calc(18px + 2 * (100vw - 300px) / 1300); } }
    @media (min-width: 1600px) {
      .zen-text--text-xl {
        font-size: 20px; } }
  .zen-text--text-lg {
    font-size: 15px; }
    @media (min-width: 300px) {
      .zen-text--text-lg {
        font-size: calc(15px + 3 * (100vw - 300px) / 1300); } }
    @media (min-width: 1600px) {
      .zen-text--text-lg {
        font-size: 18px; } }
  .zen-text--text-md {
    font-size: 13px; }
    @media (min-width: 300px) {
      .zen-text--text-md {
        font-size: calc(13px + 3 * (100vw - 300px) / 1300); } }
    @media (min-width: 1600px) {
      .zen-text--text-md {
        font-size: 16px; } }
  .zen-text--text-sm {
    font-size: 11px; }
    @media (min-width: 300px) {
      .zen-text--text-sm {
        font-size: calc(11px + 2 * (100vw - 300px) / 1300); } }
    @media (min-width: 1600px) {
      .zen-text--text-sm {
        font-size: 13px; } }
  .zen-text--text-xs {
    font-size: 10px; }
    @media (min-width: 300px) {
      .zen-text--text-xs {
        font-size: calc(10px + 2 * (100vw - 300px) / 1300); } }
    @media (min-width: 1600px) {
      .zen-text--text-xs {
        font-size: 12px; } }
  .zen-text--text-h1 {
    font-size: 26px;
    font-family: "Old Growth", Helvetica, Arial, sans-serif; }
    @media (min-width: 300px) {
      .zen-text--text-h1 {
        font-size: calc(26px + 10 * (100vw - 300px) / 1300); } }
    @media (min-width: 1600px) {
      .zen-text--text-h1 {
        font-size: 36px; } }
  .zen-text--text-h2 {
    font-size: 22px;
    font-family: "Old Growth", Helvetica, Arial, sans-serif; }
    @media (min-width: 300px) {
      .zen-text--text-h2 {
        font-size: calc(22px + 6 * (100vw - 300px) / 1300); } }
    @media (min-width: 1600px) {
      .zen-text--text-h2 {
        font-size: 28px; } }
  .zen-text--text-h3 {
    font-size: 20px;
    font-family: "Old Growth", Helvetica, Arial, sans-serif; }
    @media (min-width: 300px) {
      .zen-text--text-h3 {
        font-size: calc(20px + 4 * (100vw - 300px) / 1300); } }
    @media (min-width: 1600px) {
      .zen-text--text-h3 {
        font-size: 24px; } }
  .zen-text--text-h4 {
    font-size: 18px;
    font-family: "Old Growth", Helvetica, Arial, sans-serif; }
    @media (min-width: 300px) {
      .zen-text--text-h4 {
        font-size: calc(18px + 4 * (100vw - 300px) / 1300); } }
    @media (min-width: 1600px) {
      .zen-text--text-h4 {
        font-size: 22px; } }
  .zen-text--text-h5 {
    font-size: 16px;
    font-family: "Old Growth", Helvetica, Arial, sans-serif; }
    @media (min-width: 300px) {
      .zen-text--text-h5 {
        font-size: calc(16px + 2 * (100vw - 300px) / 1300); } }
    @media (min-width: 1600px) {
      .zen-text--text-h5 {
        font-size: 18px; } }
  .zen-text--text-h6 {
    font-size: 15px;
    font-family: "Old Growth", Helvetica, Arial, sans-serif; }
    @media (min-width: 300px) {
      .zen-text--text-h6 {
        font-size: calc(15px + 2 * (100vw - 300px) / 1300); } }
    @media (min-width: 1600px) {
      .zen-text--text-h6 {
        font-size: 17px; } }
  .zen-text--font-light {
    font-family: "IBM Plex Sans", Helvetica, Arial, sans-serif; }
  .zen-text--font-semibold, .zen-article__title, .sppb-article-title, .zen-article__tags, .sppb-article-meta, .zen-article-secondary__title, .zen-article-secondary__tags {
    font-family: "IBM Plex Sans SemiBold", Helvetica, Arial, sans-serif; }

/*------------------------------------------------------------------------------
// Extend text element & modifiers
//----------------------------------------------------------------------------*/
/*==============================================================================
// File:        _list.scss
// Package:     Joomla / Mr Zen
// Synopsis:    Shared list component - SASS
//============================================================================*/
.zen-list, .zen-card__content ul, .zen-pagination__inner, .zen-blog__article-pagination ul {
  padding: 0;
  margin: 0;
  /*==============================================================================
// Modifiers
//============================================================================*/
  /*------------------------------------------------------------------------------
	// Standard menu list item
	//----------------------------------------------------------------------------*/
  /*------------------------------------------------------------------------------
	// Pagination
	//----------------------------------------------------------------------------*/
  /*------------------------------------------------------------------------------
	// Font Size Changes
	//----------------------------------------------------------------------------*/ }
  .zen-list--no-type, .zen-blog__article-pagination ul {
    list-style-type: none; }
  .zen-list--default, .zen-card__content ul {
    list-style-type: disc; }
    .zen-list--default li, .zen-card__content ul li {
      line-height: 24px;
      margin: 0 0 0 15px;
      display: list-item; }
  .zen-list--default-ticks, .zen-rte--default-list-ticks ul {
    list-style-type: none;
    padding: 0 0 0 15px;
    position: relative;
    margin: 0 0 30px; }
    .zen-list--default-ticks li, .zen-rte--default-list-ticks ul li {
      padding: 0 0 5px 10px;
      margin: 0 0 5px; }
      .zen-list--default-ticks li:before, .zen-rte--default-list-ticks ul li:before {
        font-size: 16px;
        position: absolute;
        margin: 2px 0 0 0;
        content: "\e813";
        color: #0095CC;
        left: 0; }
  .zen-list--default-crosses, .zen-rte--default-list-crosses ul {
    list-style-type: none;
    padding: 0 0 0 15px;
    position: relative;
    margin: 0 0 30px; }
    .zen-list--default-crosses li, .zen-rte--default-list-crosses ul li {
      padding: 0 0 5px 10px;
      margin: 0 0 5px; }
      .zen-list--default-crosses li:before, .zen-rte--default-list-crosses ul li:before {
        font-size: 16px;
        position: absolute;
        margin: 2px 0 0 0;
        content: "\e800";
        color: #0095CC;
        left: 0; }
  .zen-list--inline li, .zen-footer__menu .zen-list--menu li {
    margin: 0 20px 0 0;
    display: inline-block; }
  .zen-list--space-even, .zen-tab__container {
    justify-content: space-evenly;
    -webkit-align-items: center;
    display: -webkit-flex;
    align-items: center;
    flex-wrap: nowrap;
    display: flex;
    width: 100%; }
    .zen-list--space-even li, .zen-tab__container li {
      width: 100%;
      margin: 0; }
  .zen-list--space-between {
    justify-content: space-between;
    -webkit-align-items: center;
    display: -webkit-flex;
    align-items: center;
    flex-wrap: nowrap;
    display: flex; }
    .zen-list--space-between li {
      margin: 0 30px 0 0;
      display: inline-block; }
  .zen-list--facet {
    list-style-type: none; }
    .zen-list--facet li {
      margin: 10px 0;
      position: relative; }
      .zen-list--facet li:before {
        content: "";
        display: block;
        position: absolute;
        border: 2px solid #0095CC;
        z-index: 5;
        border-radius: 6px;
        height: 20px;
        width: 20px;
        right: 0;
        left: 0; }
      .zen-list--facet li:after {
        content: "";
        display: block;
        position: absolute;
        -webkit-transition: all 0.5s ease 0s;
        -moz-transition: all 0.5s ease 0s;
        -ms-transition: all 0.5s ease 0s;
        -o-transition: all 0.5s ease 0s;
        transition: all 0.5s ease 0s;
        -webkit-transform: scale(0);
        z-index: 5;
        background: #FFF;
        transform: scale(0);
        border-radius: 3px;
        height: 12px;
        width: 12px;
        opacity: 0;
        left: 4px;
        top: 4px; }
      .zen-list--facet li:hover, .zen-list--facet li:focus {
        cursor: pointer; }
        .zen-list--facet li:hover:after, .zen-list--facet li:focus:after {
          content: "";
          display: block;
          position: absolute;
          -webkit-transition: all 0.2s ease 0s;
          -moz-transition: all 0.2s ease 0s;
          -ms-transition: all 0.2s ease 0s;
          -o-transition: all 0.2s ease 0s;
          transition: all 0.2s ease 0s;
          -webkit-transform: scale(1);
          background: #C6C6C6;
          transform: scale(1);
          height: 12px;
          width: 12px;
          opacity: 1;
          left: 4px;
          top: 4px; }
      .zen-list--facet li.active {
        padding: 0; }
        .zen-list--facet li.active:after {
          content: "";
          display: block;
          position: absolute;
          -webkit-transform: scale(1);
          background: #C6C6C6;
          transform: scale(1);
          height: 12px;
          width: 12px;
          opacity: 1;
          left: 4px;
          top: 4px; }
  .zen-list--filter-tags {
    list-style-type: none;
    text-align: center;
    overflow-x: hidden;
    max-height: 150px;
    overflow-y: auto;
    margin: 0; }
    .zen-list--filter-tags li {
      display: inline-block;
      margin: 0 5px 5px 0; }
  .zen-list--filter-item {
    list-style-type: none;
    margin: 0 -15px; }
    .zen-list--filter-item li {
      position: relative;
      padding: 15px;
      margin: 0; }
      .zen-list--filter-item li.active {
        background: #F2F2F2;
        font-weight: bold; }
  .zen-list--menu {
    text-transform: none; }
    .zen-list--menu li {
      display: block;
      margin: 0; }
  .zen-list--menu-dropdown {
    text-transform: none; }
    .zen-list--menu-dropdown li {
      display: block;
      margin: 0; }
  .zen-list--menu-main, .zen-menu__main .zen-list--menu {
    justify-content: space-evenly;
    display: -webkit-flex;
    display: flex;
    height: 100%; }
    .zen-list--menu-main li, .zen-menu__main .zen-list--menu li {
      -webkit-align-items: center;
      display: -webkit-flex;
      align-items: center;
      display: flex;
      margin: 0; }
  .zen-list--menu-sub, .zen-menu__main .zen-list--menu-dropdown {
    background: #1E3339;
    color: #FFF;
    border-radius: 0;
    min-width: 200px;
    border: none;
    margin: 0; }
    .zen-list--menu-sub li, .zen-menu__main .zen-list--menu-dropdown li {
      border-bottom: 1px solid #C6C6C6;
      display: -webkit-flex;
      display: flex; }
  .zen-list--menu-mobile {
    font-size: 15px; }
    .zen-list--menu-mobile li {
      padding: 0 10px 5px;
      margin: 0 0 10px;
      display: block; }
  .zen-list--pagination, .zen-pagination__inner {
    justify-content: flex-end;
    list-style-type: none;
    display: flex; }
  .zen-list--text-df {
    font-size: 14px; }
    @media (min-width: 300px) {
      .zen-list--text-df {
        font-size: calc(14px + 1 * (100vw - 300px) / 1300); } }
    @media (min-width: 1600px) {
      .zen-list--text-df {
        font-size: 15px; } }
  .zen-list--text-xl {
    font-size: 18px; }
    @media (min-width: 300px) {
      .zen-list--text-xl {
        font-size: calc(18px + 2 * (100vw - 300px) / 1300); } }
    @media (min-width: 1600px) {
      .zen-list--text-xl {
        font-size: 20px; } }
  .zen-list--text-lg {
    font-size: 15px; }
    @media (min-width: 300px) {
      .zen-list--text-lg {
        font-size: calc(15px + 3 * (100vw - 300px) / 1300); } }
    @media (min-width: 1600px) {
      .zen-list--text-lg {
        font-size: 18px; } }
  .zen-list--text-md {
    font-size: 13px; }
    @media (min-width: 300px) {
      .zen-list--text-md {
        font-size: calc(13px + 3 * (100vw - 300px) / 1300); } }
    @media (min-width: 1600px) {
      .zen-list--text-md {
        font-size: 16px; } }
  .zen-list--text-sm {
    font-size: 11px; }
    @media (min-width: 300px) {
      .zen-list--text-sm {
        font-size: calc(11px + 2 * (100vw - 300px) / 1300); } }
    @media (min-width: 1600px) {
      .zen-list--text-sm {
        font-size: 13px; } }
  .zen-list--text-xs {
    font-size: 10px; }
    @media (min-width: 300px) {
      .zen-list--text-xs {
        font-size: calc(10px + 2 * (100vw - 300px) / 1300); } }
    @media (min-width: 1600px) {
      .zen-list--text-xs {
        font-size: 12px; } }

/*------------------------------------------------------------------------------
// Extend text element & modifiers
//----------------------------------------------------------------------------*/
/*==============================================================================
// File:        _link.scss
// Package:     Joomla / Mr Zen
// Synopsis:    Shared link component - SASS
//============================================================================*/
.zen-link, .zen-pagination__link, .zen-blog__article-pagination li, .zen-blog__article-pagination a, a:not([href]):not([tabindex]), .pagination .page-item .page-link {
  text-decoration: none;
  transition: none;
  color: #0095CC;
  /*==============================================================================
// Elements
//============================================================================*/
  /*==============================================================================
// Modifiers
//============================================================================*/
  /*------------------------------------------------------------------------------
  // Pagination Link Classes
  //----------------------------------------------------------------------------*/
  /*------------------------------------------------------------------------------
  // Animated Items
  //----------------------------------------------------------------------------*/
  /*------------------------------------------------------------------------------
  // Menu Link Items
  //----------------------------------------------------------------------------*/
  /*------------------------------------------------------------------------------
	// Font Size Changes
	//----------------------------------------------------------------------------*/
  /*------------------------------------------------------------------------------
	// Font Family Changes
	//----------------------------------------------------------------------------*/ }
  .zen-link:hover, .zen-pagination__link:hover, .zen-blog__article-pagination li:hover, .zen-blog__article-pagination a:hover, a:hover:not([href]):not([tabindex]), .pagination .page-item .page-link:hover, .zen-link:focus, .zen-pagination__link:focus, .zen-blog__article-pagination li:focus, .zen-blog__article-pagination a:focus, a:focus:not([href]):not([tabindex]), .pagination .page-item .page-link:focus {
    color: #048abb;
    text-decoration: none;
    cursor: pointer; }
  .zen-link__icon {
    font-size: 30px; }
  .zen-link--invert {
    color: #048abb; }
    .zen-link--invert:hover, .zen-link--invert:focus {
      color: #0095CC; }
  .zen-link--default, .zen-blog__article-pagination a, .zen-footer__menu .zen-link, .zen-footer__menu .zen-pagination__link, .zen-footer__menu .zen-blog__article-pagination li, .zen-blog__article-pagination .zen-footer__menu li, .zen-footer__menu .zen-blog__article-pagination a, .zen-blog__article-pagination .zen-footer__menu a, .zen-footer__menu .pagination .page-item .page-link, .pagination .page-item .zen-footer__menu .page-link, a:not([href]):not([tabindex]) {
    color: #23383C; }
    .zen-link--default:hover, .zen-blog__article-pagination a:hover, .zen-footer__menu .zen-link:hover, .zen-footer__menu .zen-pagination__link:hover, .zen-footer__menu .zen-blog__article-pagination li:hover, .zen-blog__article-pagination .zen-footer__menu li:hover, .zen-footer__menu .pagination .page-item .page-link:hover, .pagination .page-item .zen-footer__menu .page-link:hover, a:hover:not([href]):not([tabindex]), .zen-link--default:focus, .zen-blog__article-pagination a:focus, .zen-footer__menu .zen-link:focus, .zen-footer__menu .zen-pagination__link:focus, .zen-footer__menu .zen-blog__article-pagination li:focus, .zen-blog__article-pagination .zen-footer__menu li:focus, .zen-footer__menu .pagination .page-item .page-link:focus, .pagination .page-item .zen-footer__menu .page-link:focus, a:focus:not([href]):not([tabindex]) {
      color: #c6194c; }
  .zen-link--default-underline {
    color: #23383C; }
    .zen-link--default-underline:hover, .zen-link--default-underline:focus {
      color: #c6194c;
      text-decoration: underline; }
  .zen-link--default-full-underline, a.zen-underline:not([href]):not([tabindex]) {
    text-decoration: underline;
    color: #23383C; }
    .zen-link--default-full-underline:hover, a.zen-underline:hover:not([href]):not([tabindex]), .zen-link--default-full-underline:focus, a.zen-underline:focus:not([href]):not([tabindex]) {
      color: #c6194c;
      text-decoration: underline; }
  .zen-link--base-grey-full-underline {
    text-decoration: underline;
    color: #777; }
    .zen-link--base-grey-full-underline:hover, .zen-link--base-grey-full-underline:focus {
      color: #777;
      text-decoration: underline; }
  .zen-link--light, .zen-breadcrumbs--light .zen-link, .zen-body__breadcrumbs .light-breadcrumbs .zen-breadcrumbs .zen-link, .zen-body__breadcrumbs .light-breadcrumbs .breadcrumb .zen-link, .zen-breadcrumbs--light .zen-pagination__link, .zen-body__breadcrumbs .light-breadcrumbs .zen-breadcrumbs .zen-pagination__link, .zen-body__breadcrumbs .light-breadcrumbs .breadcrumb .zen-pagination__link, .zen-breadcrumbs--light .zen-blog__article-pagination li, .zen-blog__article-pagination .zen-breadcrumbs--light li, .zen-body__breadcrumbs .light-breadcrumbs .zen-breadcrumbs .zen-blog__article-pagination li, .zen-blog__article-pagination .zen-body__breadcrumbs .light-breadcrumbs .zen-breadcrumbs li, .zen-body__breadcrumbs .light-breadcrumbs .breadcrumb .zen-blog__article-pagination li, .zen-blog__article-pagination .zen-body__breadcrumbs .light-breadcrumbs .breadcrumb li, .zen-breadcrumbs--light .zen-blog__article-pagination a, .zen-blog__article-pagination .zen-breadcrumbs--light a, .zen-body__breadcrumbs .light-breadcrumbs .zen-breadcrumbs .zen-blog__article-pagination a, .zen-blog__article-pagination .zen-body__breadcrumbs .light-breadcrumbs .zen-breadcrumbs a, .zen-body__breadcrumbs .light-breadcrumbs .breadcrumb .zen-blog__article-pagination a, .zen-blog__article-pagination .zen-body__breadcrumbs .light-breadcrumbs .breadcrumb a, .zen-breadcrumbs--light a:not([href]):not([tabindex]), .zen-body__breadcrumbs .light-breadcrumbs .zen-breadcrumbs a:not([href]):not([tabindex]), .zen-body__breadcrumbs .light-breadcrumbs .breadcrumb a:not([href]):not([tabindex]), .zen-breadcrumbs--light .pagination .page-item .page-link, .pagination .page-item .zen-breadcrumbs--light .page-link, .zen-body__breadcrumbs .light-breadcrumbs .zen-breadcrumbs .pagination .page-item .page-link, .pagination .page-item .zen-body__breadcrumbs .light-breadcrumbs .zen-breadcrumbs .page-link, .zen-body__breadcrumbs .light-breadcrumbs .breadcrumb .pagination .page-item .page-link, .pagination .page-item .zen-body__breadcrumbs .light-breadcrumbs .breadcrumb .page-link {
    color: #FFF; }
    .zen-link--light:hover, .zen-breadcrumbs--light .zen-link:hover, .zen-body__breadcrumbs .light-breadcrumbs .zen-breadcrumbs .zen-link:hover, .zen-body__breadcrumbs .light-breadcrumbs .breadcrumb .zen-link:hover, .zen-breadcrumbs--light .zen-pagination__link:hover, .zen-body__breadcrumbs .light-breadcrumbs .zen-breadcrumbs .zen-pagination__link:hover, .zen-body__breadcrumbs .light-breadcrumbs .breadcrumb .zen-pagination__link:hover, .zen-breadcrumbs--light .zen-blog__article-pagination li:hover, .zen-blog__article-pagination .zen-breadcrumbs--light li:hover, .zen-body__breadcrumbs .light-breadcrumbs .zen-breadcrumbs .zen-blog__article-pagination li:hover, .zen-blog__article-pagination .zen-body__breadcrumbs .light-breadcrumbs .zen-breadcrumbs li:hover, .zen-body__breadcrumbs .light-breadcrumbs .breadcrumb .zen-blog__article-pagination li:hover, .zen-blog__article-pagination .zen-body__breadcrumbs .light-breadcrumbs .breadcrumb li:hover, .zen-breadcrumbs--light .zen-blog__article-pagination a:hover, .zen-blog__article-pagination .zen-breadcrumbs--light a:hover, .zen-body__breadcrumbs .light-breadcrumbs .zen-breadcrumbs .zen-blog__article-pagination a:hover, .zen-blog__article-pagination .zen-body__breadcrumbs .light-breadcrumbs .zen-breadcrumbs a:hover, .zen-body__breadcrumbs .light-breadcrumbs .breadcrumb .zen-blog__article-pagination a:hover, .zen-blog__article-pagination .zen-body__breadcrumbs .light-breadcrumbs .breadcrumb a:hover, .zen-breadcrumbs--light a:hover:not([href]):not([tabindex]), .zen-body__breadcrumbs .light-breadcrumbs .zen-breadcrumbs a:hover:not([href]):not([tabindex]), .zen-body__breadcrumbs .light-breadcrumbs .breadcrumb a:hover:not([href]):not([tabindex]), .zen-breadcrumbs--light .pagination .page-item .page-link:hover, .pagination .page-item .zen-breadcrumbs--light .page-link:hover, .zen-body__breadcrumbs .light-breadcrumbs .zen-breadcrumbs .pagination .page-item .page-link:hover, .pagination .page-item .zen-body__breadcrumbs .light-breadcrumbs .zen-breadcrumbs .page-link:hover, .zen-body__breadcrumbs .light-breadcrumbs .breadcrumb .pagination .page-item .page-link:hover, .pagination .page-item .zen-body__breadcrumbs .light-breadcrumbs .breadcrumb .page-link:hover, .zen-link--light:focus, .zen-breadcrumbs--light .zen-link:focus, .zen-body__breadcrumbs .light-breadcrumbs .zen-breadcrumbs .zen-link:focus, .zen-body__breadcrumbs .light-breadcrumbs .breadcrumb .zen-link:focus, .zen-breadcrumbs--light .zen-pagination__link:focus, .zen-body__breadcrumbs .light-breadcrumbs .zen-breadcrumbs .zen-pagination__link:focus, .zen-body__breadcrumbs .light-breadcrumbs .breadcrumb .zen-pagination__link:focus, .zen-breadcrumbs--light .zen-blog__article-pagination li:focus, .zen-blog__article-pagination .zen-breadcrumbs--light li:focus, .zen-body__breadcrumbs .light-breadcrumbs .zen-breadcrumbs .zen-blog__article-pagination li:focus, .zen-blog__article-pagination .zen-body__breadcrumbs .light-breadcrumbs .zen-breadcrumbs li:focus, .zen-body__breadcrumbs .light-breadcrumbs .breadcrumb .zen-blog__article-pagination li:focus, .zen-blog__article-pagination .zen-body__breadcrumbs .light-breadcrumbs .breadcrumb li:focus, .zen-breadcrumbs--light .zen-blog__article-pagination a:focus, .zen-blog__article-pagination .zen-breadcrumbs--light a:focus, .zen-body__breadcrumbs .light-breadcrumbs .zen-breadcrumbs .zen-blog__article-pagination a:focus, .zen-blog__article-pagination .zen-body__breadcrumbs .light-breadcrumbs .zen-breadcrumbs a:focus, .zen-body__breadcrumbs .light-breadcrumbs .breadcrumb .zen-blog__article-pagination a:focus, .zen-blog__article-pagination .zen-body__breadcrumbs .light-breadcrumbs .breadcrumb a:focus, .zen-breadcrumbs--light a:focus:not([href]):not([tabindex]), .zen-body__breadcrumbs .light-breadcrumbs .zen-breadcrumbs a:focus:not([href]):not([tabindex]), .zen-body__breadcrumbs .light-breadcrumbs .breadcrumb a:focus:not([href]):not([tabindex]), .zen-breadcrumbs--light .pagination .page-item .page-link:focus, .pagination .page-item .zen-breadcrumbs--light .page-link:focus, .zen-body__breadcrumbs .light-breadcrumbs .zen-breadcrumbs .pagination .page-item .page-link:focus, .pagination .page-item .zen-body__breadcrumbs .light-breadcrumbs .zen-breadcrumbs .page-link:focus, .zen-body__breadcrumbs .light-breadcrumbs .breadcrumb .pagination .page-item .page-link:focus, .pagination .page-item .zen-body__breadcrumbs .light-breadcrumbs .breadcrumb .page-link:focus {
      color: #FFF; }
  .zen-link--dark {
    color: #262626; }
    .zen-link--dark:hover, .zen-link--dark:focus {
      color: #262626; }
  .zen-link--primary, a.zen-link--primary:not([href]):not([tabindex]) {
    color: #0095CC; }
    .zen-link--primary:hover, a.zen-link--primary:hover:not([href]):not([tabindex]), .zen-link--primary:focus, a.zen-link--primary:focus:not([href]):not([tabindex]) {
      color: #048abb; }
  .zen-link--secondary {
    color: #EE1656; }
    .zen-link--secondary:hover, .zen-link--secondary:focus {
      color: #f02d67; }
  .zen-link--image-zoom, .zen-article a, .sppb-addon-article a, .zen-article-secondary a {
    overflow: hidden; }
    .zen-link--image-zoom img, .zen-article a img, .sppb-addon-article a img, .zen-article-secondary a img {
      -webkit-transition: transform 1s ease 0.1s;
      -moz-transition: transform 1s ease 0.1s;
      -ms-transition: transform 1s ease 0.1s;
      -o-transition: transform 1s ease 0.1s;
      transition: transform 1s ease 0.1s; }
    .zen-link--image-zoom:hover img, .zen-article a:hover img, .sppb-addon-article a:hover img, .zen-article-secondary a:hover img {
      transform: scale(1.15); }
  .zen-link--image-overlay-expand .zen-card__image-overlay {
    -webkit-transition: height 0.2s ease-in-out 0s;
    -moz-transition: height 0.2s ease-in-out 0s;
    -ms-transition: height 0.2s ease-in-out 0s;
    -o-transition: height 0.2s ease-in-out 0s;
    transition: height 0.2s ease-in-out 0s;
    height: 40px; }
    .zen-link--image-overlay-expand .zen-card__image-overlay .overlay-message {
      -webkit-transition: all 0.5s ease-in-out 0s;
      -moz-transition: all 0.5s ease-in-out 0s;
      -ms-transition: all 0.5s ease-in-out 0s;
      -o-transition: all 0.5s ease-in-out 0s;
      transition: all 0.5s ease-in-out 0s;
      font-size: 15px;
      text-transform: none;
      position: absolute;
      visibility: hidden;
      margin: 0 auto;
      opacity: 0;
      right: 0;
      top: 25%;
      left: 0; }
  .zen-link--image-overlay-expand:hover .zen-card__image-overlay {
    height: 100%; }
    .zen-link--image-overlay-expand:hover .zen-card__image-overlay .overlay-message {
      -webkit-transition: all 1s ease-in-out 0.2s;
      -moz-transition: all 1s ease-in-out 0.2s;
      -ms-transition: all 1s ease-in-out 0.2s;
      -o-transition: all 1s ease-in-out 0.2s;
      transition: all 1s ease-in-out 0.2s;
      visibility: visible;
      opacity: 1; }
  .zen-link--facet {
    padding: 0 0 0 30px;
    z-index: 20;
    position: relative;
    line-height: 21px;
    display: block; }
  .zen-link--filter-tag {
    font-family: "IBM Plex Sans SemiBold", Helvetica, Arial, sans-serif;
    padding: 5px 35px 5px 20px;
    background: #F2F2F2;
    text-transform: uppercase;
    color: #394c50;
    border-radius: 30px;
    position: relative;
    text-align: center;
    display: block; }
    .zen-link--filter-tag:after {
      content: "";
      display: block;
      position: absolute;
      margin: 0 0 0 5px;
      content: "\e800";
      right: 15px;
      top: 9px; }
  .zen-link--filter-item {
    justify-content: space-between;
    -webkit-align-items: center;
    text-transform: uppercase;
    display: -webkit-flex;
    display: flex; }
  .zen-link--facet-heading {
    text-transform: uppercase;
    font-size: 13px;
    margin: 10px 0 15px;
    font-weight: bold;
    display: block;
    padding: 0; }
    .zen-link--facet-heading .icon-arrow-down {
      display: none; }
    .zen-link--facet-heading .icon-arrow-up {
      display: inline-block; }
    .zen-link--facet-heading.collapsed .icon-arrow-down {
      display: inline-block; }
    .zen-link--facet-heading.collapsed .icon-arrow-up {
      display: none; }
  .zen-link--pagination, .zen-pagination__link, .zen-blog__article-pagination li {
    border: 1px solid #0095CC;
    background: #FFF;
    justify-content: center;
    align-items: center;
    border-radius: 5px;
    display: flex;
    height: 40px;
    color: #23383C;
    width: 40px;
    margin: 3px; }
    .zen-link--pagination:hover, .zen-pagination__link:hover, .zen-blog__article-pagination li:hover, .zen-link--pagination:focus, .zen-pagination__link:focus, .zen-blog__article-pagination li:focus {
      background-color: #F2F2F2; }
  .zen-link--pagination-alternate, .zen-pagination__item-alternate a, .zen-pagination__item-alternate span {
    color: #FFF !important;
    background: #0095CC;
    border-radius: 50px; }
    .zen-link--pagination-alternate:hover, .zen-pagination__item-alternate a:hover, .zen-pagination__item-alternate span:hover, .zen-link--pagination-alternate:focus, .zen-pagination__item-alternate a:focus, .zen-pagination__item-alternate span:focus {
      background-color: #048abb; }
  .zen-link--pagination-active, .zen-list--pagination span, .zen-pagination__inner span, .pagination .page-item.active .page-link {
    border-color: #0095CC;
    background: #0095CC;
    color: #FFF; }
  .zen-link--bounce {
    animation-timing-function: cubic-bezier(0.28, 0.84, 0.42, 1);
    animation-iteration-count: infinite;
    animation-name: scrollBounce;
    transform-origin: center;
    animation-duration: 4s; }

@keyframes scrollBounce {
  0% {
    transform: translateY(0); }
  30% {
    transform: translateY(-10px); }
  50% {
    transform: translateY(0); }
  80% {
    transform: translateY(-7px); }
  100% {
    transform: translateY(0); } }
  .zen-link--bg-hover-light {
    -webkit-transition: all 0.3s ease 0s;
    -moz-transition: all 0.3s ease 0s;
    -ms-transition: all 0.3s ease 0s;
    -o-transition: all 0.3s ease 0s;
    transition: all 0.3s ease 0s;
    background: #FFF; }
    .zen-link--bg-hover-light:hover {
      background: #F2F2F2; }
      .zen-link--bg-hover-light:hover :first-child {
        background: #F2F2F2; }
  .zen-link--menu-main, .zen-menu__main .nav-header,
  .zen-menu__main .nav-link {
    font-size: 15px;
    font-family: "IBM Plex Sans SemiBold", Helvetica, Arial, sans-serif;
    line-height: 20px;
    text-decoration: none;
    padding: 15px;
    color: #23383C; }
    @media (min-width: 992px) {
      .zen-link--menu-main, .zen-menu__main .nav-header,
      .zen-menu__main .nav-link {
        font-size: calc(15px + 3 * (100vw - 992px) / 608); } }
    @media (min-width: 1600px) {
      .zen-link--menu-main, .zen-menu__main .nav-header,
      .zen-menu__main .nav-link {
        font-size: 18px; } }
    .zen-link--menu-main:hover, .zen-menu__main .nav-header:hover,
    .zen-menu__main .nav-link:hover, .zen-link--menu-main:focus, .zen-menu__main .nav-header:focus,
    .zen-menu__main .nav-link:focus {
      color: #394c50; }
  .zen-link--menu-sub {
    padding: 10px 15px;
    text-transform: capitalize;
    color: #FFF; }
    .zen-link--menu-sub:hover, .zen-link--menu-sub:focus {
      background: #23383C;
      color: #C6C6C6; }
  .zen-link--menu-mobile, .zen-menu__main .top-item a {
    font-family: "IBM Plex Sans SemiBold", Helvetica, Arial, sans-serif;
    border-bottom: 1px solid #C6C6C6;
    text-transform: uppercase;
    font-size: 15px;
    text-decoration: none;
    display: inline-block;
    padding: 15px 10px;
    color: #23383C;
    width: 100%; }
    .zen-link--menu-mobile:hover, .zen-menu__main .top-item a:hover, .zen-link--menu-mobile:focus, .zen-menu__main .top-item a:focus {
      color: #C6C6C6; }
  .zen-link--text-df {
    font-size: 14px; }
    @media (min-width: 300px) {
      .zen-link--text-df {
        font-size: calc(14px + 1 * (100vw - 300px) / 1300); } }
    @media (min-width: 1600px) {
      .zen-link--text-df {
        font-size: 15px; } }
  .zen-link--text-xl {
    font-size: 18px; }
    @media (min-width: 300px) {
      .zen-link--text-xl {
        font-size: calc(18px + 2 * (100vw - 300px) / 1300); } }
    @media (min-width: 1600px) {
      .zen-link--text-xl {
        font-size: 20px; } }
  .zen-link--text-lg {
    font-size: 15px; }
    @media (min-width: 300px) {
      .zen-link--text-lg {
        font-size: calc(15px + 3 * (100vw - 300px) / 1300); } }
    @media (min-width: 1600px) {
      .zen-link--text-lg {
        font-size: 18px; } }
  .zen-link--text-md {
    font-size: 13px; }
    @media (min-width: 300px) {
      .zen-link--text-md {
        font-size: calc(13px + 3 * (100vw - 300px) / 1300); } }
    @media (min-width: 1600px) {
      .zen-link--text-md {
        font-size: 16px; } }
  .zen-link--text-sm {
    font-size: 11px; }
    @media (min-width: 300px) {
      .zen-link--text-sm {
        font-size: calc(11px + 2 * (100vw - 300px) / 1300); } }
    @media (min-width: 1600px) {
      .zen-link--text-sm {
        font-size: 13px; } }
  .zen-link--text-xs {
    font-size: 10px; }
    @media (min-width: 300px) {
      .zen-link--text-xs {
        font-size: calc(10px + 2 * (100vw - 300px) / 1300); } }
    @media (min-width: 1600px) {
      .zen-link--text-xs {
        font-size: 12px; } }
  .zen-link--text-h1 {
    font-size: 26px;
    font-family: "Old Growth", Helvetica, Arial, sans-serif; }
    @media (min-width: 300px) {
      .zen-link--text-h1 {
        font-size: calc(26px + 10 * (100vw - 300px) / 1300); } }
    @media (min-width: 1600px) {
      .zen-link--text-h1 {
        font-size: 36px; } }
  .zen-link--text-h2 {
    font-size: 22px;
    font-family: "Old Growth", Helvetica, Arial, sans-serif; }
    @media (min-width: 300px) {
      .zen-link--text-h2 {
        font-size: calc(22px + 6 * (100vw - 300px) / 1300); } }
    @media (min-width: 1600px) {
      .zen-link--text-h2 {
        font-size: 28px; } }
  .zen-link--text-h3 {
    font-size: 20px;
    font-family: "Old Growth", Helvetica, Arial, sans-serif; }
    @media (min-width: 300px) {
      .zen-link--text-h3 {
        font-size: calc(20px + 4 * (100vw - 300px) / 1300); } }
    @media (min-width: 1600px) {
      .zen-link--text-h3 {
        font-size: 24px; } }
  .zen-link--text-h4 {
    font-size: 18px;
    font-family: "Old Growth", Helvetica, Arial, sans-serif; }
    @media (min-width: 300px) {
      .zen-link--text-h4 {
        font-size: calc(18px + 4 * (100vw - 300px) / 1300); } }
    @media (min-width: 1600px) {
      .zen-link--text-h4 {
        font-size: 22px; } }
  .zen-link--text-h5 {
    font-size: 16px;
    font-family: "Old Growth", Helvetica, Arial, sans-serif; }
    @media (min-width: 300px) {
      .zen-link--text-h5 {
        font-size: calc(16px + 2 * (100vw - 300px) / 1300); } }
    @media (min-width: 1600px) {
      .zen-link--text-h5 {
        font-size: 18px; } }
  .zen-link--text-h6 {
    font-size: 15px;
    font-family: "Old Growth", Helvetica, Arial, sans-serif; }
    @media (min-width: 300px) {
      .zen-link--text-h6 {
        font-size: calc(15px + 2 * (100vw - 300px) / 1300); } }
    @media (min-width: 1600px) {
      .zen-link--text-h6 {
        font-size: 17px; } }
  .zen-link--font-light {
    font-family: "IBM Plex Sans", Helvetica, Arial, sans-serif; }
  .zen-link--font-semibold, .zen-footer__menu .zen-link, .zen-footer__menu .zen-pagination__link, .zen-footer__menu .zen-blog__article-pagination li, .zen-blog__article-pagination .zen-footer__menu li, .zen-footer__menu .zen-blog__article-pagination a, .zen-blog__article-pagination .zen-footer__menu a, .zen-footer__menu a:not([href]):not([tabindex]), .zen-footer__menu .pagination .page-item .page-link, .pagination .page-item .zen-footer__menu .page-link {
    font-family: "IBM Plex Sans SemiBold", Helvetica, Arial, sans-serif; }

/*------------------------------------------------------------------------------
// Extend link element & modifiers
//----------------------------------------------------------------------------*/
/*==============================================================================
// File:        _cta.scss
// Package:     Joomla / Mr Zen
// Synopsis:    Shared cta component - SASS
//============================================================================*/
.zen-cta {
  position: relative;
  padding: 15px;
  color: #23383C;
  /*==============================================================================
// Elements
//============================================================================*/
  /*------------------------------------------------------------------------------
	// Mr Zen SPPB Feature Box
	//----------------------------------------------------------------------------*/
  /*------------------------------------------------------------------------------
	// Mr Zen SPPB Feature Button
	//----------------------------------------------------------------------------*/
  /*==============================================================================
// Modifiers
//============================================================================*/
  /*------------------------------------------------------------------------------
	// Font Size Changes
	//----------------------------------------------------------------------------*/ }
  .zen-cta__image-overlay-content, .sppb-addon-overlay-image .overlay-image-title {
    -webkit-box-shadow: 0 0 10px -2px rgba(38, 38, 38, 0.3);
    -moz-box-shadow: 0 0 10px -2px rgba(38, 38, 38, 0.3);
    box-shadow: 0 0 10px -2px rgba(38, 38, 38, 0.3);
    background: rgba(38, 38, 38, 0.6);
    z-index: 10;
    position: relative;
    padding: 20px;
    width: 100%; }
    .zen-cta__image-overlay-content:hover, .sppb-addon-overlay-image .overlay-image-title:hover, .zen-cta__image-overlay-content:focus, .sppb-addon-overlay-image .overlay-image-title:focus {
      cursor: pointer; }
    .zen-cta__image-overlay-content--icon {
      padding-left: 60px; }
      .zen-cta__image-overlay-content--icon:before {
        padding: 3px 0 0 4px;
        position: absolute;
        border-radius: 50%;
        content: "\e81f";
        font-size: 28px;
        display: block;
        height: 36px;
        width: 36px;
        left: 13px;
        top: 15px; }
  .zen-cta__image-overlay-title, .sppb-addon-overlay-image .overlay-image-title .sppb-addon-title {
    font-size: 16px;
    color: #FFF;
    transform: none; }
    @media (min-width: 300px) {
      .zen-cta__image-overlay-title, .sppb-addon-overlay-image .overlay-image-title .sppb-addon-title {
        font-size: calc(16px + 2 * (100vw - 300px) / 1300); } }
    @media (min-width: 1600px) {
      .zen-cta__image-overlay-title, .sppb-addon-overlay-image .overlay-image-title .sppb-addon-title {
        font-size: 18px; } }
    .zen-cta__image-overlay-title a, .sppb-addon-overlay-image .overlay-image-title .sppb-addon-title a {
      text-decoration: none;
      position: relative;
      color: #FFF;
      bottom: auto;
      right: auto;
      left: auto;
      top: auto; }
  .zen-cta__image-overlay-subtitle, .sppb-addon-overlay-image .overlay-image-title .sppb-addon-subtitle {
    font-size: 13px;
    font-family: "IBM Plex Sans SemiBold", Helvetica, Arial, sans-serif;
    color: #FFF;
    transform: none; }
    @media (min-width: 300px) {
      .zen-cta__image-overlay-subtitle, .sppb-addon-overlay-image .overlay-image-title .sppb-addon-subtitle {
        font-size: calc(13px + 2 * (100vw - 300px) / 1300); } }
    @media (min-width: 1600px) {
      .zen-cta__image-overlay-subtitle, .sppb-addon-overlay-image .overlay-image-title .sppb-addon-subtitle {
        font-size: 15px; } }
  .zen-cta__text {
    color: #23383C; }
  .zen-cta__blog-card {
    border: 1px solid #C6C6C6;
    background: #FFF; }
  .zen-cta__featurebox-image {
    -webkit-align-items: center;
    justify-content: center;
    display: -webkit-flex;
    align-items: center;
    display: flex;
    margin: 0 auto 25px; }
  .zen-cta__featurebox-icon {
    font-size: 64px;
    color: #0095CC; }
    @media (min-width: 300px) {
      .zen-cta__featurebox-icon {
        font-size: calc(64px + 23 * (100vw - 300px) / 1300); } }
    @media (min-width: 1600px) {
      .zen-cta__featurebox-icon {
        font-size: 87px; } }
  .zen-cta__featurebox-content {
    font-size: 18px;
    font-family: "IBM Plex Sans SemiBold", Helvetica, Arial, sans-serif;
    color: #FFF;
    text-transform: uppercase;
    padding: 10px 0; }
    @media (min-width: 300px) {
      .zen-cta__featurebox-content {
        font-size: calc(18px + 4 * (100vw - 300px) / 1300); } }
    @media (min-width: 1600px) {
      .zen-cta__featurebox-content {
        font-size: 22px; } }
  .zen-cta__featurebutton-image {
    margin: 0 15px 0 0; }
  .zen-cta__featurebutton-icon {
    font-size: 42px;
    margin: 0 15px 0 0;
    line-height: 1em;
    color: #0095CC; }
    @media (min-width: 300px) {
      .zen-cta__featurebutton-icon {
        font-size: calc(42px + 22 * (100vw - 300px) / 1300); } }
    @media (min-width: 1600px) {
      .zen-cta__featurebutton-icon {
        font-size: 64px; } }
  .zen-cta__featurebutton-title {
    font-size: 18px;
    font-family: "IBM Plex Sans SemiBold", Helvetica, Arial, sans-serif;
    text-transform: uppercase;
    color: #23383C; }
    @media (min-width: 300px) {
      .zen-cta__featurebutton-title {
        font-size: calc(18px + 4 * (100vw - 300px) / 1300); } }
    @media (min-width: 1600px) {
      .zen-cta__featurebutton-title {
        font-size: 22px; } }
  .zen-cta--no-pad {
    padding: 0; }
  .zen-cta--btn {
    display: inline-block;
    text-align: center; }
    .zen-cta--btn:hover, .zen-cta--btn:focus {
      text-decoration: none; }
  .zen-cta--featurebox {
    color: #23383C;
    text-align: center;
    margin: 0 0 30px;
    padding: 20px; }
    .zen-cta--featurebox.box-primary {
      border-color: #0095CC;
      background: #0095CC;
      text-align: left; }
  .zen-cta--featurebutton {
    -webkit-align-items: center;
    justify-content: center;
    display: -webkit-flex;
    align-items: center;
    display: flex;
    border: 1px solid #1E3339;
    background: #1E3339;
    color: #FFF;
    text-align: center;
    margin: 0 0 30px;
    padding: 10px; }
  .zen-cta--text-df {
    font-size: 14px; }
    @media (min-width: 300px) {
      .zen-cta--text-df {
        font-size: calc(14px + 1 * (100vw - 300px) / 1300); } }
    @media (min-width: 1600px) {
      .zen-cta--text-df {
        font-size: 15px; } }
  .zen-cta--text-xl {
    font-size: 18px; }
    @media (min-width: 300px) {
      .zen-cta--text-xl {
        font-size: calc(18px + 2 * (100vw - 300px) / 1300); } }
    @media (min-width: 1600px) {
      .zen-cta--text-xl {
        font-size: 20px; } }
  .zen-cta--text-lg {
    font-size: 15px; }
    @media (min-width: 300px) {
      .zen-cta--text-lg {
        font-size: calc(15px + 3 * (100vw - 300px) / 1300); } }
    @media (min-width: 1600px) {
      .zen-cta--text-lg {
        font-size: 18px; } }
  .zen-cta--text-md {
    font-size: 13px; }
    @media (min-width: 300px) {
      .zen-cta--text-md {
        font-size: calc(13px + 3 * (100vw - 300px) / 1300); } }
    @media (min-width: 1600px) {
      .zen-cta--text-md {
        font-size: 16px; } }
  .zen-cta--text-sm {
    font-size: 11px; }
    @media (min-width: 300px) {
      .zen-cta--text-sm {
        font-size: calc(11px + 2 * (100vw - 300px) / 1300); } }
    @media (min-width: 1600px) {
      .zen-cta--text-sm {
        font-size: 13px; } }
  .zen-cta--text-xs {
    font-size: 10px; }
    @media (min-width: 300px) {
      .zen-cta--text-xs {
        font-size: calc(10px + 2 * (100vw - 300px) / 1300); } }
    @media (min-width: 1600px) {
      .zen-cta--text-xs {
        font-size: 12px; } }

/*------------------------------------------------------------------------------
// Extend cta element & modifiers
//----------------------------------------------------------------------------*/
/*==============================================================================
// File:        _pill.scss
// Package:     Joomla / Mr Zen
// Synopsis:    Shared pill component - SASS
//============================================================================*/
.zen-pill {
  font-family: "IBM Plex Sans SemiBold", Helvetica, Arial, sans-serif;
  border-radius: 30px;
  text-transform: uppercase;
  display: inline-block;
  padding: 10px 30px;
  background: #F2F2F2;
  color: #23383C;
  /*==============================================================================
// Modifiers
//============================================================================*/ }
  .zen-pill--bg-medium-grey {
    background: #DDD; }
  .zen-pill--bg-dark-grey {
    background: #8F8F8F; }
  .zen-pill--price {
    font-size: 15px; }
    @media (max-width: 767.98px) {
      .zen-pill--price {
        font-size: 13px; } }
    @media (max-width: 575.98px) {
      .zen-pill--price {
        font-size: 12px; } }
  .zen-pill--mega-tab {
    border: 1px solid #C6C6C6;
    background: #FFF;
    text-decoration: none;
    margin: 20px 20px 0;
    text-align: center;
    width: 100%; }
    .zen-pill--mega-tab:hover, .zen-pill--mega-tab:focus {
      border-color: #0095CC;
      background: #0095CC;
      color: #FFF;
      cursor: pointer; }
    .zen-pill--mega-tab[aria-selected="true"] {
      border-color: #0095CC;
      background: #0095CC;
      color: #FFF; }

/*==============================================================================
// File:        _map.scss
// Package:     Joomla / Mr Zen
// Synopsis:    Shared map component - SASS
//============================================================================*/
.zen-map {
  height: 350px;
  overflow: hidden;
  margin: 30px 0;
  width: 100%;
  /*==============================================================================
// Elements
//============================================================================*/ }
  @media (min-width: 300px) {
    .zen-map {
      height: calc(350px + 500 * (100vw - 300px) / 1300); } }
  @media (min-width: 1600px) {
    .zen-map {
      height: 850px; } }
  .zen-map .leaflet-popup-content {
    width: 310px !important;
    padding: 0; }
  .zen-map.leaflet-container h4 {
    font-size: 20px;
    text-align: left;
    color: #0095CC;
    padding: 0; }
  .zen-map.leaflet-container p {
    padding: 0 0 15px;
    text-align: left; }
  .zen-map.leaflet-container a {
    text-align: center; }
  .zen-map__info {
    color: #0095CC;
    padding: 15px; }
  .zen-map__sub-title {
    text-transform: uppercase; }

/*==============================================================================
// File:        _tag.scss
// Package:     Joomla / Mr Zen
// Synopsis:    Shared tag component - SASS
//============================================================================*/
.zen-tag {
  padding: 5px 15px 5px 25px;
  text-transform: uppercase;
  font-size: 13px;
  color: #FFF;
  font-weight: bold;
  line-height: 1em;
  /*==============================================================================
// Modifiers
//============================================================================*/ }
  .zen-tag--default {
    background: var(--orange); }
  .zen-tag--primary {
    background: #0095CC; }
  .zen-tag--secondary {
    background: #EE1656; }

/*==============================================================================
// File:        _tab.scss
// Package:     Joomla / Mr Zen
// Synopsis:    Shared tab component - SASS
//============================================================================*/
.zen-tab {
  padding: 0;
  /*==============================================================================
// Elements
//============================================================================*/ }
  .zen-tab__heading-item {
    display: inline-block; }
    @media (max-width: 767.98px) {
      .zen-tab__heading-item {
        width: 50%; } }
    @media (max-width: 575.98px) {
      .zen-tab__heading-item {
        width: 100%; } }
  .zen-tab__content-pane {
    padding: 40px 0;
    display: none; }
    .zen-tab__content-pane--active {
      display: block; }
    .zen-tab__content-pane--no-pad {
      padding: 0; }
  .zen-tab__link {
    padding-right: 15px;
    padding-left: 15px;
    font-family: "IBM Plex Sans SemiBold", Helvetica, Arial, sans-serif;
    text-transform: uppercase;
    padding-bottom: 15px;
    background: #FFF;
    padding-top: 15px;
    margin: 0 5px 0 0;
    position: relative;
    text-align: center;
    display: block;
    color: #23383C; }
    @media (min-width: 992px) {
      .zen-tab__link {
        padding-right: calc(10px + 5 * (100vw - 992px) / 608); } }
    @media (min-width: 1600px) {
      .zen-tab__link {
        padding-right: 15px; } }
    @media (min-width: 992px) {
      .zen-tab__link {
        padding-left: calc(10px + 5 * (100vw - 992px) / 608); } }
    @media (min-width: 1600px) {
      .zen-tab__link {
        padding-left: 15px; } }
    @media (max-width: 1199.98px) {
      .zen-tab__link {
        padding-right: 10px;
        padding-left: 10px;
        font-size: 13px; } }
  @media (max-width: 1199.98px) and (min-width: 992px) {
    .zen-tab__link {
      padding-right: calc(5px + 5 * (100vw - 992px) / 608); } }
  @media (max-width: 1199.98px) and (min-width: 1600px) {
    .zen-tab__link {
      padding-right: 10px; } }
  @media (max-width: 1199.98px) and (min-width: 992px) {
    .zen-tab__link {
      padding-left: calc(5px + 5 * (100vw - 992px) / 608); } }
  @media (max-width: 1199.98px) and (min-width: 1600px) {
    .zen-tab__link {
      padding-left: 10px; } }
    .zen-tab__link:hover, .zen-tab__link:focus {
      text-decoration: none;
      background: #0095CC;
      color: #FFF; }
    .zen-tab__link.active {
      background: #0095CC;
      color: #FFF; }
    .zen-tab__link.current {
      background: #0095CC;
      color: #FFF; }

/*------------------------------------------------------------------------------
// Extend tab element & modifiers
//----------------------------------------------------------------------------*/
/*==============================================================================
// File:        _notice.scss
// Package:     Joomla / Mr Zen
// Synopsis:    Shared notice component - SASS
//============================================================================*/
.zen-notice {
  padding: 0;
  /*==============================================================================
// Elements
//============================================================================*/ }
  .zen-notice__inner {
    flex-direction: column;
    position: relative;
    color: #FFF;
    min-height: 150px;
    min-width: 100%;
    padding: 10px;
    display: flex; }
  .zen-notice__main {
    text-align: center;
    margin: auto; }
  .zen-notice__info {
    align-self: flex-end;
    position: absolute;
    bottom: 10px;
    right: 10px; }
  .zen-notice--light {
    background: #1aa0d1; }
  .zen-notice--mid {
    background: #0095CC; }
  .zen-notice--dark {
    background: #048abb; }

/*------------------------------------------------------------------------------
// Extend notice element & modifiers
//----------------------------------------------------------------------------*/
/* Generic styles */
/*==============================================================================
// File:        _styles.scss
// Package:     Joomla / Mr Zen
// Synopsis:    Main styles for the website - SASS
//============================================================================*/
/*------------------------------------------------------------------------------
// Layout Styles
//----------------------------------------------------------------------------*/
html {
  overflow-x: hidden; }

body {
  font-size: 15px;
  font-family: "IBM Plex Sans", Helvetica, Arial, sans-serif;
  font-weight: normal;
  color: #23383C; }
  @media (min-width: 300px) {
    body {
      font-size: calc(15px + 1 * (100vw - 300px) / 1300); } }
  @media (min-width: 1600px) {
    body {
      font-size: 16px; } }
  @media (max-width: 991.98px) {
    body {
      overflow-x: hidden;
      display: inherit; } }

@media (max-width: 575.98px) {
  h1, h2, h3, .zen-search__title, h4 {
    overflow-wrap: break-word; } }

a {
  color: #0095CC; }
  a:hover, a:focus {
    color: #1aa0d1; }

p {
  margin-bottom: 15px; }

iframe {
  border: none; }

.zen-back-to-top {
  z-index: 20;
  position: fixed;
  display: none;
  bottom: 10px;
  left: 45%; }
  @media (max-width: 991.98px) {
    .zen-back-to-top {
      display: block; } }

.zen-overlay {
  background: transparent;
  /* Rollback to clear BG */
  background: rgba(38, 38, 38, 0.7);
  position: fixed;
  display: none;
  height: 100%;
  z-index: 15;
  width: 100%;
  opacity: 0;
  top: 0; }

/*------------------------------------------------------------------------------
// Example Class Animation for Lazyload
//----------------------------------------------------------------------------*/
.zen-lazy-load {
  position: relative;
  display: block;
  z-index: 1000;
  height: 1px;
  width: 1px; }

.in-viewport {
  -webkit-transition: all 0.3s ease 0s;
  -moz-transition: all 0.3s ease 0s;
  -ms-transition: all 0.3s ease 0s;
  -o-transition: all 0.3s ease 0s;
  transition: all 0.3s ease 0s;
  visibility: hidden;
  opacity: 0; }
  .in-viewport.active-viewport {
    -webkit-transition: all 0.3s ease 0s;
    -moz-transition: all 0.3s ease 0s;
    -ms-transition: all 0.3s ease 0s;
    -o-transition: all 0.3s ease 0s;
    transition: all 0.3s ease 0s;
    visibility: visible;
    opacity: 1; }

.avPlayerBlock .zen-lazy-load {
  position: absolute;
  top: 0; }

/*------------------------------------------------------------------------------
// Override Google Recapcha Badge (3.0)
//----------------------------------------------------------------------------*/
.grecaptcha-badge {
  display: none !important; }

/*------------------------------------------------------------------------------
// Video Fade In Animation
//----------------------------------------------------------------------------*/
.section-bg-video {
  opacity: 0;
  transition: opacity 1s ease-in-out;
}

.section-bg-video.video-loaded {
  opacity: 1;
}

/*------------------------------------------------------------------------------
// Zen Main Styles
//----------------------------------------------------------------------------*/
.zen-flex-center {
  -webkit-align-items: center;
  justify-content: center;
  display: -webkit-flex;
  align-items: center;
  display: flex; }

.zen-flex-end {
  -webkit-align-items: flex-end;
  justify-content: flex-end;
  display: -webkit-flex;
  align-items: flex-end;
  display: flex; }

.zen-bg-default {
  background: #C6C6C6; }

.zen-bg-default-dark {
  background: #394c50; }

.zen-bg-light {
  background: #F2F2F2; }

.zen-bg-dark {
  background: #23383C; }

.zen-bg-darker {
  background: #1E3339; }

.zen-valid-error {
  border-color: var(--pink);
  background-color: #F2F2F2;
  color: var(--red); }

.zen-error-text {
  font-size: 15px;
  color: var(--red); }

.zen-white-bg {
  background: #FFF; }

.zen-emphasized {
  font-style: normal; }

.zen-no-border {
  border: none; }

.zen-no-shadow {
  box-shadow: none; }

.zen-bold {
  font-weight: bold; }

.zen-capitalize {
  text-transform: capitalize; }

.zen-line-through {
  text-decoration: line-through; }

.zen-hide {
  display: none; }

.zen-inline-block {
  display: inline-block; }

.zen-overflow-hidden {
  overflow: hidden; }

.zen-no-transform {
  text-transform: none; }

.zen-uppercase {
  text-transform: uppercase; }

.zen-underline {
  text-decoration: underline; }

.zen-nomargin {
  margin: 0; }

.zen-row {
  margin: 0 -15px; }

.zen-col {
  float: left; }

.zen-width-full {
  width: 100%; }

.zen-width-half {
  width: 50%; }

.zen-nopad {
  padding: 0; }

.zen-nopad-top {
  padding-top: 0; }

.zen-nopad-right {
  padding-right: 0; }

.zen-nopad-bottom {
  padding-bottom: 0; }

.zen-nopad-left {
  padding-left: 0; }

/*------------------------------------------------------------------------------
// Login Page Styling
//----------------------------------------------------------------------------*/
.profile {
  padding: 15px 0; }
  .profile .profile-core {
    margin: 0 0 15px; }

.profile-edit {
  padding: 15px 0; }

/*------------------------------------------------------------------------------
// Error Page Styling
//----------------------------------------------------------------------------*/
.error-page .jumbotron {
  border-top: 1px solid #FFF;
  background: #1aa0d1; }

/* Layout styles */
/*==============================================================================
// File:        _header.scss
// Package:     Joomla / Mr Zen
// Synopsis:    Main header styles for the website - SASS
//============================================================================*/
.zen-header {
  z-index: 20;
  position: relative;
  padding: 0;
  /*==============================================================================
// Elements
//============================================================================*/ }
  @media (max-width: 991.98px) {
    .zen-header {
      z-index: 100;
      position: fixed;
      top: 0px;
      left: 0px;
      right: 0px;
      background: #FFF; } }
  .zen-header__main {
    -webkit-box-shadow: 0 1px 10px 0 rgba(38, 38, 38, 0.3);
    -moz-box-shadow: 0 1px 10px 0 rgba(38, 38, 38, 0.3);
    box-shadow: 0 1px 10px 0 rgba(38, 38, 38, 0.3);
    z-index: 10;
    position: relative; }
  .zen-header__logo {
    -webkit-align-items: center;
    justify-content: center;
    display: -webkit-flex;
    align-items: center;
    padding: 0;
    display: flex; }
    @media (max-width: 991.98px) {
      .zen-header__logo {
        justify-content: flex-start; }
        .zen-header__logo img {
          max-width: 130px; } }
    .zen-header__logo p {
      margin: 0; }
  @media (max-width: 991.98px) {
    .zen-header__menu {
      max-width: 0; } }
  .zen-header__info {
    -webkit-align-items: center;
    justify-content: flex-end;
    display: -webkit-flex;
    align-items: center;
    display: flex; }
  .zen-header__number {
    font-family: "IBM Plex Sans SemiBold", Helvetica, Arial, sans-serif;
    -webkit-align-items: center;
    justify-content: center;
    background: #23383C;
    align-items: center;
    color: #FFF;
    padding: 10px;
    height: 100%; }
    @media (min-width: 992px) {
      .zen-header__number {
        width: 100%; } }
    .zen-header__number .custom {
      display: inline-block; }
    .zen-header__number p {
      margin: 0; }
  .zen-header__link {
    font-family: "IBM Plex Sans SemiBold", Helvetica, Arial, sans-serif;
    background: #0095CC;
    text-decoration: none;
    padding: 30px 25px;
    color: #FFF;
    height: 100%; }
    .zen-header__link:hover, .zen-header__link:focus {
      background: #048abb;
      color: #FFF; }
    @media (max-width: 991.98px) {
      .zen-header__link {
        padding: 10px; } }

.zen-top-warning {
  max-width: unset !important; }

/*==============================================================================
// File:        _body.scss
// Package:     Joomla / Mr Zen
// Synopsis:    Main body styles for the website - SASS
//============================================================================*/
.zen-body {
  padding: 0;
  /*==============================================================================
// Elements
//============================================================================*/ }
  /* @media (max-width: 991.98px) {
    .zen-body {
      margin-top: 50px; } } */
  .zen-body__main {
    padding: 60px 0 0; }
  .zen-body__breadcrumbs {
    padding: 0; }
    @media (max-width: 991.98px) {
      .zen-body__breadcrumbs {
        border-top: 1px solid #F2F2F2; } }
    .zen-body__breadcrumbs .moduletable {
      position: relative; }

/*==============================================================================
// File:        _blog.scss
// Package:     Joomla / Mr Zen
// Synopsis:    Main blog styles for the website - SASS
//============================================================================*/
.zen-blog {
  background: #F2F2F2;
  padding: 0; }
  .zen-blog__article-body h2, .zen-blog__article-body h3, .zen-blog__article-body .zen-search__title, .zen-blog__article-body h4, .zen-blog__article-body h5, .zen-blog__article-body h6 {
    font-family: "IBM Plex Sans SemiBold", Helvetica, Arial, sans-serif; }
  .zen-blog__article-info {
    font-family: "IBM Plex Sans SemiBold", Helvetica, Arial, sans-serif;
    text-transform: uppercase; }
  .zen-blog__article-pagination {
    justify-content: flex-end;
    display: -webkit-flex;
    display: flex; }
    .zen-blog__article-pagination ul {
      display: -webkit-flex;
      display: flex; }
    .zen-blog__article-pagination li {
      padding: 0 30px; }
      .zen-blog__article-pagination li .icon-chevron-right,
      .zen-blog__article-pagination li .icon-chevron-left {
        display: none; }
  .zen-blog__article-image {
    display: flex;
    align-items: center;
    justify-content: center; }

/*==============================================================================
// File:        _content-search.scss
// Package:     Joomla / Mr Zen
// Synopsis:    Article search results styling for the website - SASS
//============================================================================*/
.zen-content-search {
  padding: 0;
  /*==============================================================================
// Elements
//============================================================================*/ }
  .zen-content-search__main-panel {
    background: #F2F2F2; }
  .zen-content-search__input {
    display: -webkit-flex;
    position: relative;
    margin: 0 0 15px;
    flex-wrap: wrap;
    display: flex; }
  .zen-content-search__buttons {
    display: -webkit-flex;
    display: flex; }
  .zen-content-search__filters {
    border: 1px solid #C6C6C6;
    background: #FFF;
    margin: 20px 0; }
  @media (max-width: 991.98px) {
    .zen-content-search__filter-string .inner {
      border-bottom: 1px solid #C6C6C6; } }
  .zen-content-search__filter-string .radio {
    margin-right: 10px; }
  .zen-content-search__filter-string .radio input {
    margin-right: 5px; }
  @media (max-width: 991.98px) {
    .zen-content-search__filter-category .inner {
      border-bottom: 1px solid #C6C6C6; } }
  .zen-content-search__filter-category .checkbox {
    margin-right: 10px; }
  .zen-content-search__filter-category .checkbox input {
    margin-right: 5px; }
  .zen-content-search__results-topper {
    justify-content: space-between;
    display: -webkit-flex;
    display: flex; }
  .zen-content-search__result-count {
    -webkit-align-items: center;
    display: -webkit-flex;
    align-items: center;
    display: flex; }
  .zen-content-search__result-select {
    -webkit-align-items: center;
    display: -webkit-flex;
    align-items: center;
    display: flex; }
    .zen-content-search__result-select select {
      padding: 10px; }
    .zen-content-search__result-select span {
      padding-right: 20px; }

/*==============================================================================
// File:        _footer.scss
// Package:     Joomla / Mr Zen
// Synopsis:    Main footer styles for the website - SASS
//============================================================================*/
.zen-footer {
  background: #FFF;
  position: relative;
  max-width: 1920px;
  margin: 0 auto;
  /*==============================================================================
// Elements
//============================================================================*/ }
  /* .zen-footer:after {
    padding-top: 31.25%;
    background: url("https://i.assetzen.net/i/Qe7E5zewGla1/w:1920/h:600/q:70.webp") no-repeat left top transparent;
    background-size: contain;
    position: relative;
    display: block;
    content: "";
    bottom: 0;
    right: 0;
    left: 0;
    top: 0; } */
  .zen-footer__contact {
    background: #F2F2F2; }
  .zen-footer__menu {
    font-size: 15px;
    z-index: 10;
    position: relative;
    margin: 50px 0 0; }
    @media (min-width: 300px) {
      .zen-footer__menu {
        font-size: calc(15px + 3 * (100vw - 300px) / 1300); } }
    @media (min-width: 1600px) {
      .zen-footer__menu {
        font-size: 18px; } }
  .zen-footer__copyright {
    color: #8F8F8F;
    z-index: 10;
    position: relative;
    padding: 15px; }

/* Module styles */
/*==============================================================================
// File:        _quick-search.scss
// Package:     Joomla / Mr Zen
// Synopsis:    Quick Search module styling - SASS
//============================================================================*/
.zen-quicksearch {
  -webkit-box-shadow: 0 0 18px -2px rgba(38, 38, 38, 0.3);
  -moz-box-shadow: 0 0 18px -2px rgba(38, 38, 38, 0.3);
  box-shadow: 0 0 18px -2px rgba(38, 38, 38, 0.3);
  background: #F2F2F2;
  margin: 0 auto 15px;
  max-width: 690px;
  padding: 10px; }
  @media (max-width: 767.98px) {
    .zen-quicksearch {
      max-width: 300px; } }
  .zen-quicksearch .spinner-border {
    height: 1em;
    width: 1em; }

/* Accommodation component styles */
/* Holiday component styles */
/*==============================================================================
// File:        _main.scss
// Package:     Joomla / Mr Zen
// Synopsis:    Main holiday template - SASS
//============================================================================*/
.zen-holiday {
  background: #F2F2F2;
  padding: 0;
  /*==============================================================================
// Elements
//============================================================================*/ }
  .zen-holiday__info-bar {
    background: #394c50;
    text-transform: uppercase;
    position: relative;
    color: #FFF;
    padding: 15px 0;
    z-index: 1; }
    @media (max-width: 991.98px) {
      .zen-holiday__info-bar {
        padding: 0 0 25px; } }
    @media (max-width: 400px) {
      .zen-holiday__info-bar .zen-text, .zen-holiday__info-bar .zen-article__title, .zen-holiday__info-bar .sppb-article-title, .zen-holiday__info-bar .zen-article__tags, .zen-holiday__info-bar .sppb-article-meta {
        font-size: 14px; } }
    .zen-holiday__info-bar .grade-image {
      max-height: 40px; }
      @media (max-width: 991.98px) {
        .zen-holiday__info-bar .grade-image {
          max-height: 30px; } }
    .zen-holiday__info-bar .zen-custom-justify {
      justify-content: flex-start; }
      @media (max-width: 991.98px) {
        .zen-holiday__info-bar .zen-custom-justify {
          justify-content: flex-end; } }
      @media (max-width: 575.98px) {
        .zen-holiday__info-bar .zen-custom-justify {
          justify-content: center; } }
  .zen-holiday__info-bar-image {
    position: absolute;
    margin: 0 15px;
    z-index: 1;
    bottom: 0;
    right: 0; }
    @media (max-width: 991.98px) {
      .zen-holiday__info-bar-image {
        text-align: center;
        bottom: -10px;
        left: 0; }
        .zen-holiday__info-bar-image img {
          max-width: 155px; } }
    @media (max-width: 767.98px) {
      .zen-holiday__info-bar-image img {
        max-width: 125px; } }
    @media (max-width: 400px) {
      .zen-holiday__info-bar-image img {
        max-width: 100px; } }
  .zen-holiday__cta-bar {
    background: #23383C;
    padding: 15px 0; }
    @media (min-width: 992px) {
      .zen-holiday__cta-bar .zen-btn, .zen-holiday__cta-bar .btn, .zen-holiday__cta-bar a.zen-btn:not([href]):not([tabindex]), .zen-holiday__cta-bar .rsform-submit-button, .zen-holiday__cta-bar .rsform-calendar-button, .zen-holiday__cta-bar .rsform-calendar-button.btn.btn-secondary, .zen-holiday__cta-bar .zen-btn--hero .sp-slider-btn-text, .zen-btn--hero .zen-holiday__cta-bar .sp-slider-btn-text, .zen-holiday__cta-bar .sppb-btn, .zen-holiday__cta-bar .sppb-sp-slider-button .sp-slider-btn-text, .sppb-sp-slider-button .zen-holiday__cta-bar .sp-slider-btn-text, .zen-holiday__cta-bar .sppb-readmore {
        max-width: 220px; } }
  .zen-holiday__tabs {
    -webkit-box-shadow: 0 1px 10px 0 rgba(38, 38, 38, 0.3);
    -moz-box-shadow: 0 1px 10px 0 rgba(38, 38, 38, 0.3);
    box-shadow: 0 1px 10px 0 rgba(38, 38, 38, 0.3);
    position: -webkit-sticky;
    background: #23383C;
    padding: 15px 0 0;
    margin: 0 0 30px;
    position: sticky;
    z-index: 15;
    top: 137px; }
  .zen-holiday__content {
    padding: 50px 0; }
    @media (max-width: 991.98px) {
      .zen-holiday__content {
        padding: 0; } }
  .zen-holiday__content-box {
    border: 1px solid #8F8F8F;
    background: #FFF;
    padding: 50px; }
    @media (max-width: 991.98px) {
      .zen-holiday__content-box {
        margin: 0 0 20px;
        padding: 30px;
        border: none; } }
    @media (max-width: 575.98px) {
      .zen-holiday__content-box {
        padding: 30px 20px; } }
  .zen-holiday__content-item {
    display: inline-block;
    margin: 15px 0;
    width: 100%; }
  .zen-holiday__content-extra {
    border-bottom: 1px solid #C6C6C6;
    padding: 0 0 50px;
    margin: 0 0 40px; }
  .zen-holiday__gallery {
    padding: 50px 0; }
  .zen-holiday__download {
    background: #1E3339;
    color: #FFF;
    margin: 50px 0;
    padding: 30px; }
  .zen-holiday__related {
    padding: 50px 0; }

/* Location component styles */
/* Search styles */
/*==============================================================================
// File:        _main.scss
// Package:     Joomla / Mr Zen
// Synopsis:    Main search page styling - SASS
//============================================================================*/
.zen-search {
  padding: 0;
  /*==============================================================================
// Elements
//============================================================================*/ }
  .zen-search__hero {
    padding: 50px 0 15px;
    background: #F2F2F2; }
    @media (max-width: 991.98px) {
      .zen-search__hero {
        padding-bottom: 30px; } }
  .zen-search__sort {
    background: #F2F2F2;
    padding: 0 0 50px; }
    @media (max-width: 991.98px) {
      .zen-search__sort {
        background: #FFF;
        margin: 15px 0 0; } }
  .zen-search__content {
    padding: 50px 0; }
    @media (max-width: 991.98px) {
      .zen-search__content {
        padding: 0 0 15px; } }
  .zen-search__hit-loading {
    margin: 0; }
    @media (max-width: 991.98px) {
      .zen-search__hit-loading {
        display: inline-block;
        margin: 0 0 0 5px;
        height: 20px; }
        .zen-search__hit-loading .spinner-border {
          vertical-align: baseline;
          height: 1rem;
          width: 1rem; } }
  .zen-search__hit-count {
    display: block; }
  .zen-search__intro {
    padding: 0; }
  .zen-search__tag {
    position: absolute;
    top: 20px; }
  .zen-search__title {
    margin: 25px 0 10px;
    display: block; }
  .zen-search__title-label {
    display: inline-block;
    margin: 0; }
  .zen-search__sub-title {
    text-transform: uppercase;
    font-size: 12px;
    padding: 15px 10px 0;
    vertical-align: top;
    font-weight: bold; }
  @media (max-width: 767.98px) {
    .zen-search__item {
      max-width: 500px;
      margin: 0 auto; } }
  .zen-search__refine-header {
    border-bottom: 2px solid #F2F2F2;
    position: -webkit-sticky;
    background: #FFF;
    z-index: 30;
    padding: 15px 10px;
    position: sticky;
    width: 100%;
    left: 0;
    top: 0; }
    .zen-search__refine-header img {
      max-width: 20px; }
  .zen-search__refine-filters {
    border-top: 1px solid #C6C6C6;
    padding: 15px 15px 0;
    margin: 15px -15px 0; }
  .zen-search__refine-panel {
    border-bottom: 1px solid #C6C6C6;
    padding: 15px;
    display: none; }
  .zen-search__refine-footer {
    padding: 15px 10px 5px;
    background: #FFF;
    z-index: 30;
    text-align: center;
    position: fixed;
    height: 70px;
    width: 100%;
    bottom: 0;
    left: 0; }
    .zen-search__refine-footer a {
      max-width: 300px; }

/*------------------------------------------------------------------------------
// Extend menu element & modifiers
//----------------------------------------------------------------------------*/
/* External override styles */
/*==============================================================================
// File:        _slick-slider.scss
// Package:     Joomla / Mr Zen
// Synopsis:    Slick Slider styles & overrides - SASS
//============================================================================*/
.slick-slider {
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -ms-touch-action: pan-y;
  -moz-user-select: none;
  box-sizing: border-box;
  -ms-user-select: none;
  position: relative;
  user-select: none;
  display: block; }

.slick-list {
  position: relative;
  overflow: hidden;
  display: block;
  margin: 0 50px;
  padding: 0; }
  .slick-list:focus {
    outline: none; }
  .slick-list.dragging {
    cursor: pointer;
    cursor: hand; }

.slick-slider .slick-track,
.slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  -o-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0); }

.slick-track {
  display: -webkit-flex;
  position: relative;
  margin-right: auto;
  margin-left: auto;
  display: flex;
  left: 0;
  top: 0; }
  .slick-track:before, .slick-track:after {
    display: table;
    content: ""; }
  .slick-track:after {
    clear: both; }
  .slick-loading .slick-track {
    visibility: hidden; }

.slick-slide {
  -webkit-align-items: center;
  justify-content: center;
  align-items: center;
  min-height: 1px;
  display: none;
  height: 100%;
  float: left; }
  [dir="rtl"] .slick-slide {
    float: right; }
  .slick-slide img {
    border: 1px solid #FFF;
    max-width: 100%;
    display: block; }
  .slick-slide.slick-loading img {
    display: none; }
  .slick-slide.dragging img {
    pointer-events: none; }
  .slick-initialized .slick-slide {
    display: -webkit-flex;
    display: flex; }
  .slick-loading .slick-slide {
    visibility: hidden; }
  .slick-vertical .slick-slide {
    border: 1px solid transparent;
    display: block;
    height: auto; }

.slick-arrow.slick-hidden {
  display: none; }

/*------------------------------------------------------------------------------
// Slick Slider Overrides
//----------------------------------------------------------------------------*/
.slick-dots {
  margin: 25px auto 10px;
  text-align: center;
  width: 100%;
  padding: 0; }
  .slick-dots button {
    background: #DDD;
    color: transparent;
    outline: none;
    height: 15px;
    border: none;
    width: 100%; }
  .slick-dots li {
    display: inline-block;
    margin: 0 10px 0 0;
    height: 15px;
    width: 85px;
    float: none; }
    @media (max-width: 1199.98px) {
      .slick-dots li {
        width: 65px; } }
    @media (max-width: 991.98px) {
      .slick-dots li {
        width: 45px; } }
    @media (max-width: 767.98px) {
      .slick-dots li {
        width: 30px; } }
    @media (max-width: 575.98px) {
      .slick-dots li {
        width: 20px; } }
    .slick-dots li.slick-active button {
      background: #0095CC; }

.slick-slider {
  margin: 20px 0 0; }

.slick-slide {
  position: relative;
  padding: 0 10px; }

.slick-arrow {
  position: absolute;
  line-height: 1em;
  background: none;
  font-size: 75px;
  color: #0095CC;
  border: none;
  opacity: .6;
  padding: 0;
  z-index: 1;
  top: 35%; }
  @media (max-width: 575.98px) {
    .slick-arrow {
      font-size: 55px; } }
  .slick-arrow:hover, .slick-arrow:focus {
    cursor: pointer;
    outline: 0;
    opacity: 1; }
  .slick-arrow.slick-prev {
    left: 0; }
  .slick-arrow.slick-next {
    right: 0; }

/*------------------------------------------------------------------------------
// Partial Overrides
//----------------------------------------------------------------------------*/
@media (max-width: 991.98px) {
  .zen-slick-slider {
    margin-right: -15px;
    margin-left: -15px; } }

@media (max-width: 991.98px) {
  .zen-slick-slider .slick-list {
    padding: 0 10% !important;
    margin: 0; } }

@media (max-width: 767.98px) {
  .zen-slick-slider .slick-list {
    padding: 0 15% !important; } }

@media (max-width: 991.98px) {
  .zen-slick-slider .slick-arrow {
    height: 100%;
    width: 10%;
    top: 0; }
    .zen-slick-slider .slick-arrow i {
      display: none; } }

@media (max-width: 767.98px) {
  .zen-slick-slider .slick-arrow {
    width: 15%; } }

/*==============================================================================
// File:        _datepicker.scss
// Package:     Joomla / Mr Zen
// Synopsis:    Date Picker overrides - SASS
//============================================================================*/
/*------------------------------------------------------------------------------
 * Stylesheet for the Date Range Picker, for use with Bootstrap 3.x
 *
 * Copyright 2013 Dan Grossman ( http://www.dangrossman.info )
 * Licensed under the Apache License v2.0
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Built for http://www.improvely.com
/-----------------------------------------------------------------------------*/
.daterangepicker .btn-sm {
  width: 100%; }

.daterangepicker.dropdown-menu {
  max-width: none;
  z-index: 3000;
  padding: 18px; }

.daterangepicker.opensleft .calendar,
.daterangepicker.opensleft .ranges {
  float: left;
  margin: 4px; }

.daterangepicker.openscenter .calendar,
.daterangepicker.opensright .calendar,
.daterangepicker.openscenter .ranges,
.daterangepicker.opensright .ranges {
  float: right;
  margin: 4px; }

.daterangepicker.single .calendar,
.daterangepicker.single .ranges {
  float: none; }

.daterangepicker .ranges {
  margin-right: 18px !important;
  text-align: left;
  width: 165px; }

.daterangepicker .ranges .range_inputs > div {
  margin: 0 0 25px;
  float: left; }

.daterangepicker .ranges .range_inputs > div:nth-child(2) {
  padding-left: 11px; }

.daterangepicker .calendar {
  max-width: 270px;
  display: none; }

.daterangepicker.show-calendar .calendar {
  display: block; }

.daterangepicker .calendar.single .calendar-date {
  border: none; }

.daterangepicker .calendar th,
.daterangepicker .calendar td {
  white-space: nowrap;
  text-align: center;
  min-width: 32px; }

.daterangepicker .daterangepicker_start_input label,
.daterangepicker .daterangepicker_end_input label {
  text-shadow: #FFF 1px 1px 0;
  text-transform: uppercase;
  font-weight: normal;
  margin-bottom: 2px;
  line-height: 20px;
  font-size: 11px;
  display: block;
  height: 20px;
  color: #333;
  width: 74px; }

.daterangepicker .ranges input {
  font-size: 11px; }

.daterangepicker .ranges .input-mini {
  vertical-align: middle;
  border: 1px solid #CCC;
  border-radius: 4px;
  margin: 0 0 10px 0;
  line-height: 30px;
  font-size: 11px;
  padding: 0 6px;
  display: block;
  height: 30px;
  width: 74px;
  color: #555; }

.daterangepicker .ranges ul {
  list-style: none;
  padding: 0;
  margin: 0; }

.daterangepicker .ranges li {
  -webkit-border-radius: 5px;
  border: 1px solid #0095CC;
  -moz-border-radius: 5px;
  background: #0095CC;
  margin-bottom: 8px;
  border-radius: 5px;
  padding: 3px 12px;
  font-size: 13px;
  cursor: pointer;
  color: #0095CC; }

.daterangepicker .ranges li.active {
  border: 1px solid #0095CC;
  background: #0095CC;
  color: #FFF; }

.daterangepicker .ranges li:hover {
  border: 1px solid #0095CC;
  background: #0095CC;
  color: #FFF; }

.daterangepicker .ranges li.zen-filter-next {
  color: #FFF; }

.daterangepicker .calendar-date {
  background: #FFF;
  border: 1px solid #DDD;
  border-radius: 4px;
  padding: 4px; }

.daterangepicker .calendar-time {
  margin: 8px auto 0 auto;
  text-align: center;
  line-height: 30px; }

.daterangepicker {
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  background: #FFF;
  border-radius: 4px;
  position: absolute;
  margin-top: 1px;
  padding: 4px;
  top: 100px;
  left: 20px; }

.daterangepicker.opensleft:before {
  border-bottom-color: rgba(38, 38, 38, 0.2);
  border-right: 7px solid transparent;
  border-left: 7px solid transparent;
  border-bottom: 7px solid #CCC;
  display: inline-block;
  position: absolute;
  content: "";
  right: 9px;
  top: -7px; }

.daterangepicker.opensleft:after {
  border-bottom: 6px solid #FFF;
  border-right: 6px solid transparent;
  border-left: 6px solid transparent;
  display: inline-block;
  position: absolute;
  right: 10px;
  content: "";
  top: -6px; }

.daterangepicker.openscenter:before {
  border-bottom-color: rgba(38, 38, 38, 0.2);
  border-right: 7px solid transparent;
  border-left: 7px solid transparent;
  border-bottom: 7px solid #CCC;
  display: inline-block;
  margin-right: auto;
  position: absolute;
  margin-left: auto;
  content: "";
  top: -7px;
  right: 0;
  width: 0;
  left: 0; }

.daterangepicker.openscenter:after {
  border-bottom: 6px solid #FFF;
  border-right: 6px solid transparent;
  border-left: 6px solid transparent;
  display: inline-block;
  margin-right: auto;
  position: absolute;
  margin-left: auto;
  content: "";
  top: -6px;
  right: 0;
  width: 0;
  left: 0; }

.daterangepicker.opensright:before {
  border-bottom-color: rgba(38, 38, 38, 0.2);
  border-right: 7px solid transparent;
  border-left: 7px solid transparent;
  border-bottom: 7px solid #CCC;
  display: inline-block;
  position: absolute;
  content: "";
  top: -7px;
  left: 9px; }

.daterangepicker.opensright:after {
  border-bottom: 6px solid #FFF;
  border-right: 6px solid transparent;
  border-left: 6px solid transparent;
  display: inline-block;
  position: absolute;
  content: "";
  left: 10px;
  top: -6px; }

.daterangepicker table {
  width: 100%;
  margin: 0; }

.daterangepicker td, .daterangepicker th {
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  white-space: nowrap;
  border-radius: 4px;
  text-align: center;
  cursor: pointer;
  height: 20px;
  width: 20px; }

.daterangepicker td.off {
  color: #999; }

.daterangepicker option.disabled,
.daterangepicker td.disabled {
  color: #999; }

.daterangepicker td.available:hover,
.daterangepicker th.available:hover {
  background: #0095CC;
  color: #FFF; }

.daterangepicker td.in-range {
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  background: #FEF5E3;
  border-radius: 0; }

.daterangepicker td.start-date {
  -webkit-border-radius: 4px 0 0 4px;
  -moz-border-radius: 4px 0 0 4px;
  border-radius: 4px 0 0 4px; }

.daterangepicker td.end-date {
  -webkit-border-radius: 0 4px 4px 0;
  -moz-border-radius: 0 4px 4px 0;
  border-radius: 0 4px 4px 0; }

.daterangepicker td.start-date.end-date {
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px; }

.daterangepicker td.active:hover,
.daterangepicker td.active {
  background-color: #0095CC;
  border-color: #0095CC;
  color: #FFF; }

.daterangepicker td.week,
.daterangepicker th.week {
  font-size: 80%;
  color: #CCC; }

.daterangepicker select.monthselect,
.daterangepicker select.yearselect {
  cursor: default;
  font-size: 12px;
  padding: 1px;
  height: auto;
  margin: 0; }

.daterangepicker select.monthselect {
  margin-right: 2%;
  width: 56%; }

.daterangepicker select.yearselect {
  width: 40%; }

.daterangepicker select.minuteselect,
.daterangepicker select.secondselect,
.daterangepicker select.hourselect,
.daterangepicker select.ampmselect {
  margin-bottom: 0;
  width: 50px; }

.daterangepicker_start_input {
  float: left; }

.daterangepicker_end_input {
  padding-left: 11px;
  float: left; }

.daterangepicker th.month {
  width: auto; }

@media (max-width: 767.98px) {
  .daterangepicker.show-calendar .calendar {
    display: none; } }

.daterangepicker .applyBtn {
  color: #0095CC; }

/*==============================================================================
// File:        _bootstrap.scss
// Package:     Joomla / Mr Zen
// Synopsis:    Bootstrap style overrides - SASS
//============================================================================*/
/*------------------------------------------------------------------------------
// Example changing the standard spacing to 20px
// Setting all items to maximum width up to 1440px
//----------------------------------------------------------------------------*/
.container {
  padding-right: 15px;
  padding-left: 15px; }

@media (min-width: 576px) {
  .container, .container-sm {
    max-width: 760px; } }

@media (min-width: 768px) {
  .container,
  .container-sm,
  .container-md {
    max-width: 940px; } }

@media (min-width: 992px) {
  .container,
  .container-sm,
  .container-md,
  .container-lg {
    max-width: 1100px; } }

@media (min-width: 1200px) {
  .container,
  .container-sm,
  .container-md,
  .container-lg,
  .container-xl {
    max-width: 1440px; } }

@media (min-width: 1400px) {
  .container,
  .container-sm,
  .container-md,
  .container-lg,
  .container-xl,
  .container-xxl {
    max-width: 1440px; } }

.container-fluid {
  max-width: 1920px; }

.row {
  margin-right: -15px;
  margin-left: -15px; }

.col-1, .col-2, .col-3, .col-4, .col-5, .col-6, .col-7, .col-8, .col-9, .col-10, .col-11, .col-12, .col, .col-auto, .col-sm-1, .col-sm-2, .col-sm-3, .col-sm-4, .col-sm-5, .col-sm-6, .col-sm-7, .col-sm-8, .col-sm-9, .col-sm-10, .col-sm-11, .col-sm-12, .col-sm, .col-sm-auto, .col-md-1, .col-md-2, .col-md-3, .col-md-4, .col-md-5, .col-md-6, .col-md-7, .col-md-8, .col-md-9, .col-md-10, .col-md-11, .col-md-12, .col-md, .col-md-auto, .col-lg-1, .col-lg-2, .col-lg-3, .col-lg-4, .col-lg-5, .col-lg-6, .col-lg-7, .col-lg-8, .col-lg-9, .col-lg-10, .col-lg-11, .col-lg-12, .col-lg, .col-lg-auto, .col-xl-1, .col-xl-2, .col-xl-3, .col-xl-4, .col-xl-5, .col-xl-6, .col-xl-7, .col-xl-8, .col-xl-9, .col-xl-10, .col-xl-11, .col-xl-12, .col-xl, .col-xl-auto {
  padding-right: 15px;
  padding-left: 15px; }

/*------------------------------------------------------------------------------
// Placeholder overrides - Mr Zen Customisations
//----------------------------------------------------------------------------*/
.placeholder {
  overflow: hidden; }

/*------------------------------------------------------------------------------
// Use negative margin - Mr Zen Customisations
//
// Alternative to setting: $enable-negative-margins: true;
//----------------------------------------------------------------------------*/
.mt-n1 {
  margin-top: -0.25rem !important; }

.mt-n2 {
  margin-top: -0.5rem !important; }

.mt-n3 {
  margin-top: -1rem !important; }

.mt-n4 {
  margin-top: -1.5rem !important; }

.mt-n5 {
  margin-top: -3rem !important; }

/*------------------------------------------------------------------------------
// Border overrides - Mr Zen Customisations
//----------------------------------------------------------------------------*/
.border-bottom,
.border-start,
.border-top,
.border-end,
.border {
  border-color: #C6C6C6 !important; }

/*------------------------------------------------------------------------------
// Navbar overrides - Mr Zen Customisations
//----------------------------------------------------------------------------*/
.navbar .container-fluid {
  display: block; }

/*------------------------------------------------------------------------------
// Menu overrides - Mr Zen Customisations
//----------------------------------------------------------------------------*/
.zen-menu {
  /* Sub menu position */
  /* Allow menu on hover - remove if click is used */ }
  .zen-menu .dropdown-menu .dropdown .dropdown-menu {
    left: 100%;
    margin: 0;
    top: 0; }
  .zen-menu .dropdown:hover > .dropdown-menu {
    display: block; }
  .zen-menu .dropdown-toggle:after {
    vertical-align: middle;
    margin-left: 5px;
    content: "\e81d";
    color: #0095CC;
    border: none; }
  .zen-menu .dropdown-toggle[aria-expanded="true"]:after {
    transform: rotate(180deg); }

.dropdown-item.active,
.dropdown-item:active {
  background: #0095CC; }

/*------------------------------------------------------------------------------
// Card feature overrides - Mr Zen Customisations
//----------------------------------------------------------------------------*/
/*------------------------------------------------------------------------------
// Button module and class overrides - Mr Zen Customisations
//----------------------------------------------------------------------------*/
/*------------------------------------------------------------------------------
// Carousel overrides - Mr Zen Customisations
//----------------------------------------------------------------------------*/
.carousel-inner {
  display: contents; }

.carousel-control-next,
.carousel-control-prev {
  text-decoration: none;
  opacity: 1;
  width: 10%; }
  @media (max-width: 767.98px) {
    .carousel-control-next,
    .carousel-control-prev {
      width: 14%; } }
  @media (max-width: 575.98px) {
    .carousel-control-next,
    .carousel-control-prev {
      width: 18%; } }
  .carousel-control-next .carousel-control-next-icon,
  .carousel-control-next .carousel-control-prev-icon,
  .carousel-control-prev .carousel-control-next-icon,
  .carousel-control-prev .carousel-control-prev-icon {
    -webkit-transition: all 0.3s ease 0s;
    -moz-transition: all 0.3s ease 0s;
    -ms-transition: all 0.3s ease 0s;
    -o-transition: all 0.3s ease 0s;
    transition: all 0.3s ease 0s;
    border: 2px solid transparent;
    font-size: 42px;
    border-radius: 50%;
    color: #FFF;
    line-height: 60px;
    background: none;
    height: 60px;
    width: 60px; }
    @media (max-width: 767.98px) {
      .carousel-control-next .carousel-control-next-icon,
      .carousel-control-next .carousel-control-prev-icon,
      .carousel-control-prev .carousel-control-next-icon,
      .carousel-control-prev .carousel-control-prev-icon {
        font-size: 27px;
        line-height: 45px;
        height: 45px;
        width: 45px; } }
  .carousel-control-next:hover .carousel-control-next-icon,
  .carousel-control-next:hover .carousel-control-prev-icon, .carousel-control-next:focus .carousel-control-next-icon,
  .carousel-control-next:focus .carousel-control-prev-icon,
  .carousel-control-prev:hover .carousel-control-next-icon,
  .carousel-control-prev:hover .carousel-control-prev-icon,
  .carousel-control-prev:focus .carousel-control-next-icon,
  .carousel-control-prev:focus .carousel-control-prev-icon {
    border: 2px solid #FFF; }

.carousel-control-next-icon:before {
  margin: 0 0 0 4px;
  content: "\e81f"; }

.carousel-control-prev-icon:before {
  margin: 0 4px 0 0;
  content: "\e81e"; }

.animate-height {
  -webkit-transition: all 0.3s ease 0s;
  -moz-transition: all 0.3s ease 0s;
  -ms-transition: all 0.3s ease 0s;
  -o-transition: all 0.3s ease 0s;
  transition: all 0.3s ease 0s; }
  .animate-height:before {
    background: #F2F2F2;
    -webkit-align-items: center;
    font-size: 20px;
    justify-content: center;
    display: -webkit-flex;
    content: "Loading..";
    align-items: center;
    position: absolute;
    flex-flow: column;
    display: flex;
    height: 100%;
    color: #23383C;
    width: 100%; }

/*------------------------------------------------------------------------------
// Pagination overrides - Mr Zen Customisations
//----------------------------------------------------------------------------*/
/*------------------------------------------------------------------------------
// Breadcrumbs - Mr Zen Customisations
//----------------------------------------------------------------------------*/
/*------------------------------------------------------------------------------
// List Group overrides - Mr Zen Customisations
//----------------------------------------------------------------------------*/
.list-group-item {
  border-color: #DDD; }

.list-group-item.active {
  background-color: #0095CC;
  border-color: #0095CC; }

/*------------------------------------------------------------------------------
// Toast overrides - Mr Zen Customisations
//----------------------------------------------------------------------------*/
.toast {
  z-index: 20;
  position: fixed;
  left: 15px;
  top: 0; }
  .toast .toast-body {
    padding: 5px; }

/*------------------------------------------------------------------------------
// Form overrides - Mr Zen Customisations
//----------------------------------------------------------------------------*/
/*------------------------------------------------------------------------------
// Table overrides - Mr Zen Customisations
//----------------------------------------------------------------------------*/
.table {
  color: #23383C; }
  .table thead th {
    border-bottom: 1px solid #C6C6C6;
    color: #0095CC; }

.table-responsive td, .table-responsive th {
  min-width: 140px; }

.table-bordered {
  border: 1px solid #C6C6C6; }
  .table-bordered td, .table-bordered th {
    border: 1px solid #C6C6C6; }

.table-striped tbody tr:nth-of-type(odd) {
  background: #F2F2F2; }

/*------------------------------------------------------------------------------
// Modal overrides - Mr Zen Customisations
//----------------------------------------------------------------------------*/
.modal-backdrop.show {
  opacity: .75; }

/*==============================================================================
// File:        _rsforms.scss
// Package:     Joomla / Mr Zen
// Synopsis:    RSForms styling & overrides - SASS
//============================================================================*/
.rsform-block-inline {
  display: inline-block;
  vertical-align: top; }

.rsform-block-title h3, .rsform-block-title .zen-search__title {
  color: #23383C;
  margin: 0; }

.rsform-select-box,
.rsform-input-box {
  margin: 0 20px 0 0; }

.rsform-calendar-button {
  border-bottom-left-radius: 0;
  border: 1px solid #0095CC;
  border-top-left-radius: 0; }
  .rsform-calendar-button.btn.btn-secondary {
    border-bottom-left-radius: 0;
    border: 1px solid #0095CC;
    border-top-left-radius: 0; }

/*==============================================================================
// File:        _sppb.scss
// Package:     Joomla / Mr Zen
// Synopsis:    SP Page Builder overrides - SASS
//============================================================================*/
.sppb-panel-modern > .sppb-panel-heading {
  background: #0095CC;
  color: #FFF; }

.sppb-alert-primary {
  background: #0095CC;
  color: #FFF; }

.sppb-progress-bar {
  background: #0095CC; }

.animated-text-words-wrapper {
  color: #0095CC; }

.alert-warning {
  color: var(--warning); }

.sppb-blocknumber-number {
  background-color: #0095CC; }

.sppb-addon-flickr .sppb-flickr-gallery li a:before {
  background: #0095CC; }

.sppb-addon-flickr .sppb-flickr-gallery li a:hover:before {
  background: transparent; }

.image-effect-zoom-in {
  cursor: pointer; }

.pricelist-left-image img {
  max-width: 100%; }

.zen-btn--hero {
  text-decoration: none; }
  .zen-btn--hero .sp-slider-btn-text {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px; }
    .zen-btn--hero .sp-slider-btn-text:after {
      content: "\e80c";
      font-family: "fontello" !important;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      font-variant: normal;
      text-transform: none;
      font-weight: normal;
      font-style: normal;
      line-height: 1;
      speak: none;
      font-size: 30px;
      color: white; }

/*------------------------------------------------------------------------------
// Fix SP Page Builder Breakpoint Issue
//----------------------------------------------------------------------------*/
@media screen and (max-width: 768px) and (min-width: 768px) {
  .sppb-addon-image-content .sppb-col-sm-6 {
    -ms-flex: 0 0 100%;
    max-width: 100%;
    flex: 0 0 100%; } }

/*------------------------------------------------------------------------------
// Fix for adding a new row - SPPB front-end edit mode
//----------------------------------------------------------------------------*/
.sppb-row-bottom-new-row .open .sp-pagebuilder-dropdown-row-layouts {
  display: block; }

/*------------------------------------------------------------------------------
// Zen SP Page Builder Overrides
//
// Example changing the standard spacing to 20px
// Setting all items to maximum width up to 1440px
//----------------------------------------------------------------------------*/
.sppb-row-container,
.sppb-container {
  padding-right: 15px;
  padding-left: 15px; }
  @media (min-width: 576px) {
    .sppb-row-container,
    .sppb-container {
      max-width: 760px;
      width: 100%; } }
  @media (min-width: 768px) {
    .sppb-row-container,
    .sppb-container {
      max-width: 940px;
      width: 100%; } }
  @media (min-width: 992px) {
    .sppb-row-container,
    .sppb-container {
      max-width: 1100px;
      width: 100%; } }
  @media (min-width: 1200px) {
    .sppb-row-container,
    .sppb-container {
      max-width: 1440px;
      width: 100%; } }

.mod-sppagebuilder {
  max-width: 1920px;
  margin: 0 auto; }

.sp-pagebuilder-row,
.sppb-row {
  margin-right: -15px;
  margin-left: -15px; }
  .sp-pagebuilder-row.sppb-no-gutter,
  .sppb-row.sppb-no-gutter {
    margin-right: 0;
    margin-left: 0; }

.sppb-col, .sppb-col-auto, .sppb-col-lg, .sppb-col-lg-1, .sppb-col-lg-10, .sppb-col-lg-11, .sppb-col-lg-12, .sppb-col-lg-2, .sppb-col-lg-3, .sppb-col-lg-4, .sppb-col-lg-5, .sppb-col-lg-6, .sppb-col-lg-7, .sppb-col-lg-8, .sppb-col-lg-9, .sppb-col-lg-auto, .sppb-col-md, .sppb-col-md-1, .sppb-col-md-10, .sppb-col-md-11, .sppb-col-md-12, .sppb-col-md-2, .sppb-col-md-3, .sppb-col-md-4, .sppb-col-md-5, .sppb-col-md-6, .sppb-col-md-7, .sppb-col-md-8, .sppb-col-md-9, .sppb-col-md-auto, .sppb-col-sm, .sppb-col-sm-1, .sppb-col-sm-10, .sppb-col-sm-11, .sppb-col-sm-12, .sppb-col-sm-2, .sppb-col-sm-3, .sppb-col-sm-4, .sppb-col-sm-5, .sppb-col-sm-6, .sppb-col-sm-7, .sppb-col-sm-8, .sppb-col-sm-9, .sppb-col-sm-auto, .sppb-col-xl, .sppb-col-xl-1, .sppb-col-xl-10, .sppb-col-xl-11, .sppb-col-xl-12, .sppb-col-xl-2, .sppb-col-xl-3, .sppb-col-xl-4, .sppb-col-xl-5, .sppb-col-xl-6, .sppb-col-xl-7, .sppb-col-xl-8, .sppb-col-xl-9, .sppb-col-xl-auto, .sppb-col-xs-1, .sppb-col-xs-10, .sppb-col-xs-11, .sppb-col-xs-12, .sppb-col-xs-2, .sppb-col-xs-3, .sppb-col-xs-4, .sppb-col-xs-5, .sppb-col-xs-6, .sppb-col-xs-7, .sppb-col-xs-8, .sppb-col-xs-9 {
  padding-right: 15px;
  padding-left: 15px; }

/*------------------------------------------------------------------------------
// Button Module and Class Overrides - Mr Zen Customisations
//----------------------------------------------------------------------------*/
/*------------------------------------------------------------------------------
// Slideshow Module - Mr Zen Customisations
//----------------------------------------------------------------------------*/
@media (max-width: 991.98px) {
  .sp-slider .sp-dots, .sp-slider .sp-nav-control {
    height: 40px; } }

.sppb-addon-sp-slider .sp-item .sp-background:after {
  background: rgba(38, 38, 38, 0.1); }

div.sp-slider .sp-nav-control .nav-control {
  background: rgba(255, 255, 255, 0.15); }

div[data-arrow-content=icon_only].sp-slider .sp-nav-control .nav-control {
  font-size: 30px;
  height: 60px;
  width: 60px; }

div[data-arrow-content=icon_only] .sp-nav-control .nav-control i {
  line-height: 58px; }

.sp-slider .sp-nav-control .nav-control.next-control i {
  margin: 0 -2px 0 0;
  right: 5px; }

.sp-slider .sp-nav-control .nav-control.prev-control i {
  margin: 0 0 0 -2px;
  left: 5px; }

.sp-dots ul li:before {
  content: none; }

/*------------------------------------------------------------------------------
// Carousel Pro Overrides - Mr Zen Customisations
//----------------------------------------------------------------------------*/
.sppb-carousel-pro-text h2,
.sppb-carousel-text h2 {
  text-shadow: 1px 1px 10px #262626; }

.sppb-carousel-pro-text .sppb-carousel-pro-content,
.sppb-carousel-text .sppb-carousel-pro-content {
  text-shadow: 1px 1px 10px #262626; }

/*------------------------------------------------------------------------------
// Gallery Overrides - Mr Zen Customisations
//----------------------------------------------------------------------------*/
.sppb-gallery li.slick-slide {
  padding: 0 10px; }

/*------------------------------------------------------------------------------
// Image Carousel Overrides - Mr Zen Customisations
//----------------------------------------------------------------------------*/
.sppb-carousel-extended-dots ul li span {
  background: #0095CC; }

.dot-controller-position-vertical_right.sp-slider .sp-dots {
  justify-content: start;
  top: 70%; }

@media (max-width: 991.98px) {
  .sp-slider .sp-dots ul li {
    max-height: 25px;
    max-width: 25px; } }

/*------------------------------------------------------------------------------
// Featurebox & button Overrides - Mr Zen Customisations
//----------------------------------------------------------------------------*/
.zen-cta--featurebutton .sppb-addon-title,
.zen-cta--featurebox .sppb-addon-title {
  color: #FFF; }

.zen-cta--featurebox-dark .zen-cta__featurebox-content {
  color: #262626; }

.zen-cta--featurebox.box-primary .sppb-addon-content {
  font-size: 12px;
  text-transform: none; }
  @media (min-width: 300px) {
    .zen-cta--featurebox.box-primary .sppb-addon-content {
      font-size: calc(12px + 3 * (100vw - 300px) / 1300); } }
  @media (min-width: 1600px) {
    .zen-cta--featurebox.box-primary .sppb-addon-content {
      font-size: 15px; } }

/*------------------------------------------------------------------------------
// Testimonial Overrides - Mr Zen Customisations
//----------------------------------------------------------------------------*/
.sppb-testimonial-carousel-rating::before,
.sppb-testimonial-carousel-rating:after {
  content: "\e811\e811\e811\e811\e811"; }

/*------------------------------------------------------------------------------
// Image Overlay Overrides - Mr Zen Customisations
//----------------------------------------------------------------------------*/
.sppb-addon-overlay-image {
  overflow: hidden; }
  .sppb-addon-overlay-image a {
    z-index: 10;
    position: absolute;
    display: block;
    bottom: 0;
    right: 0;
    left: 0;
    top: 0; }
  .sppb-addon-overlay-image .overlay-background-style {
    display: none; }
  .sppb-addon-overlay-image .overlay-background-image-wrapper:before {
    -webkit-transition: all 0.3s ease 0s;
    -moz-transition: all 0.3s ease 0s;
    -ms-transition: all 0.3s ease 0s;
    -o-transition: all 0.3s ease 0s;
    transition: all 0.3s ease 0s;
    content: "";
    display: block;
    position: absolute;
    z-index: 5;
    bottom: 0;
    right: 0;
    left: 0;
    top: 0; }
  .sppb-addon-overlay-image:hover .overlay-background-image-wrapper:before, .sppb-addon-overlay-image:focus .overlay-background-image-wrapper:before {
    background: rgba(0, 149, 204, 0.8); }

/*------------------------------------------------------------------------------
// Title Style Overrides - Mr Zen Customisations
//----------------------------------------------------------------------------*/
.zen-no-transform .sppb-addon-title {
  text-transform: none; }

.sppb-sp-slider-title {
  text-shadow: 1px 1px 10px #262626;
  transform: none !important;
  margin: 0 0 15px; }
  @media (max-width: 767.98px) {
    .sppb-sp-slider-title {
      padding: 0 15%; } }

.sppb-sp-slider-text {
  font-size: 15px;
  text-shadow: 1px 1px 10px #262626;
  font-family: "IBM Plex Sans SemiBold", Helvetica, Arial, sans-serif;
  transform: none !important; }
  @media (min-width: 300px) {
    .sppb-sp-slider-text {
      font-size: calc(15px + 7 * (100vw - 300px) / 1300); } }
  @media (min-width: 1600px) {
    .sppb-sp-slider-text {
      font-size: 22px; } }
  @media (max-width: 767.98px) {
    .sppb-sp-slider-text {
      padding: 0 15%; } }

h1.sppb-sp-slider-title {
  font-size: 30px;
  line-height: 1.75em; }
  @media (min-width: 300px) {
    h1.sppb-sp-slider-title {
      font-size: calc(30px + 18 * (100vw - 300px) / 1300); } }
  @media (min-width: 1600px) {
    h1.sppb-sp-slider-title {
      font-size: 48px; } }

.zen-capitalize .sppb-addon-title {
  text-transform: capitalize; }

.sppb-addon-header {
  position: relative; }

/*------------------------------------------------------------------------------
// Contact Form Overrides - Mr Zen Customisations
//----------------------------------------------------------------------------*/
/*------------------------------------------------------------------------------
// Table Overrides - Mr Zen Customisations
//----------------------------------------------------------------------------*/
.sppb-addon-table-main tbody tr {
  background: #F2F2F2; }

/*------------------------------------------------------------------------------
// Remove SPPB 'z-index' CSS Class '.zen-remove-zindex'
//----------------------------------------------------------------------------*/
#sp-page-builder .page-content .sppb-section.zen-remove-zindex,
.mod-sppagebuilder .sppb-section.zen-remove-zindex,
#sp-page-builder .sppb-section.zen-remove-zindex {
  z-index: auto; }

.zen-remove-zindex .sppb-column-addons,
.zen-remove-zindex .sppb-column {
  z-index: auto; }

/*------------------------------------------------------------------------------
// Blog Flash SPPB Adjustments
//----------------------------------------------------------------------------*/
.sppb-article-meta {
  margin: 0; }

@media (max-width: 991.98px) {
  .sppb-addon-articles .sppb-col-sm-4 {
    margin-top: 30px;
    max-width: 100%;
    flex: 0 0 100%; } }

/*==============================================================================
// File:        _rte.scss
// Package:     Joomla / Mr Zen
// Synopsis:    Rich text editor overrides - SASS
//============================================================================*/
.zen-rte {
  padding: 0;
  /*==============================================================================
// Modifiers
//============================================================================*/ }
  .zen-rte--space-headings h1, .zen-rte--space-headings h2, .zen-rte--space-headings h3, .zen-rte--space-headings .zen-search__title, .zen-rte--space-headings h4, .zen-rte--space-headings h5, .zen-rte--space-headings h6 {
    margin: 0 0 1em; }

/* User agent override styles */
/*==============================================================================
// File:        _user-agent.scss
// Package:     Joomla / Mr Zen
// Synopsis:    User agent targeting - SASS
//============================================================================*/
/*------------------------------------------------------------------------------
// IE 
//----------------------------------------------------------------------------*/
/*==============================================================================
// ISSUE - https://github.com/twbs/bootstrap/issues/25242
//============================================================================*/
.ie .img-fluid {
  width: 100%; }

/* Search Results Equalizer Styles */
#results .row.equalize-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 24px;
    padding: 0 15px;
    margin: 0;
    width: 100%;
}

#results .row.equalize-grid .zen-card {
    display: flex;
    flex-direction: column;
    height: 100%;
}

#results .row.equalize-grid .zen-card__image,
#results .row.equalize-grid .zen-title,
#results .row.equalize-grid .zen-text--subtitle,
#results .row.equalize-grid .zen-card__info,
#results .row.equalize-grid .zen-price {
    height: 100%;
}

/* Mobile layout for search results */
#results .row.mobile-layout {
    display: grid;
    grid-template-columns: 1fr;
    gap: 20px;
}

#results .row.mobile-layout .zen-card {
    height: auto;
}

#results .row.mobile-layout .zen-card__image,
#results .row.mobile-layout .zen-title,
#results .row.mobile-layout .zen-text--subtitle,
#results .row.mobile-layout .zen-card__info,
#results .row.mobile-layout .zen-price {
    height: auto !important;
}

#reviews-tab:before {
    background-image: url(/templates/zenbase/icons/trips/extensions.svg);
}
