/**
 * Video Fade In Effect
 * Adds fade-in animation to background videos when they load
 */
(function() {
    'use strict';
    
    function initVideoFadeIn() {
        // Find all background videos with the section-bg-video class
        var videos = document.querySelectorAll('.section-bg-video');
        
        if (videos.length === 0) {
            return;
        }
        
        // Process each video
        for (var i = 0; i < videos.length; i++) {
            var video = videos[i];
            
            // Skip if already processed
            if (video.classList.contains('video-processed')) {
                continue;
            }
            
            // Mark as processed
            video.classList.add('video-processed');
            
            // Function to handle video load
            function handleVideoLoad() {
                this.classList.add('video-loaded');
            }
            
            // Function to handle video error
            function handleVideoError() {
                // Still fade in even if there's an error to prevent invisible video
                this.classList.add('video-loaded');
            }
            
            // Add event listeners
            video.addEventListener('loadeddata', handleVideoLoad);
            video.addEventListener('canplay', handleVideoLoad);
            video.addEventListener('error', handleVideoError);
            
            // Fallback: If video is already loaded when script runs
            if (video.readyState >= 2) { // HAVE_CURRENT_DATA or higher
                video.classList.add('video-loaded');
            }
            
            // Safety fallback: Force fade-in after 3 seconds regardless
            setTimeout(function(v) {
                return function() {
                    if (!v.classList.contains('video-loaded')) {
                        v.classList.add('video-loaded');
                    }
                };
            }(video), 3000);
        }
    }
    
    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initVideoFadeIn);
    } else {
        initVideoFadeIn();
    }
    
    // Also run when new content is dynamically added (for AJAX content)
    if (typeof MutationObserver !== 'undefined') {
        var observer = new MutationObserver(function(mutations) {
            var shouldReinit = false;
            for (var i = 0; i < mutations.length; i++) {
                var mutation = mutations[i];
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    for (var j = 0; j < mutation.addedNodes.length; j++) {
                        var node = mutation.addedNodes[j];
                        if (node.nodeType === 1) { // Element node
                            if (node.classList && node.classList.contains('section-bg-video')) {
                                shouldReinit = true;
                                break;
                            } else if (node.querySelector && node.querySelector('.section-bg-video')) {
                                shouldReinit = true;
                                break;
                            }
                        }
                    }
                }
                if (shouldReinit) break;
            }
            if (shouldReinit) {
                initVideoFadeIn();
            }
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }
})();
