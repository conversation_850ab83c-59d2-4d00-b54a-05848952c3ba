/*==============================================================================
// File:        additional.js
// Package:     Joomla / Mr Zen
// Synopsis:    Additional JavaScript Functions
//============================================================================*/

jQuery(function () {

	var gloVar = {
    passive: false,
    // Classes & IDs.
    //
    modal: ".modal",
    showBtn: "show-btn",
    showPanel: "show-panel",
    fixedMenu: "fixed-menu",
    filterPan: "filter-panel",
    imgGallry: ".zen-gallery",
    actReview: "active-review",
    mobileTab: ".js-mobile-tabs",
    dfaultTab: ".js-default-tab",
    scrlTrans: "scroll-transition",
    crdReview: ".zen-card--review",
    megaTabPill: ".zen-pill--mega-tab",
    // jQuery objects & HTML.
    //
    _htmlBody: jQuery("body"),
    _mainBody: jQuery(".zen-body"),
    _menuMark: jQuery(".sticky-marker"),
    _moblTabs: jQuery(".js-mobile-tabs"),
    _readMore: jQuery(".js-ugc-read-more"),
    _brochureButton: jQuery(".js-brochureButton"),
    _fixdHead: jQuery(".zen-header__main"),
    _ckButton: jQuery(".js-cookie-policy"),
    _ckPolicy: jQuery(".zen-panel--policy"),
    _styleSel: jQuery(".zen-styled-select, .zen-form select"),
    _bckToTop: jQuery(".js-zen-back-to-top"),
    _ovlayClk: jQuery(".js-overlay-trigger"),
    _scrollActv: jQuery(".js-scroll-activate"),
    _inputFocus: jQuery(".js-icon-focus-input"),
    _megaTabCon: jQuery(".js-zen-menu--mega-tab"),
    _megaButton: jQuery(".js-mega-dropdown-button"),
    _refinePanel: jQuery(".zen-search__refine-panel"),
    _refineHeadr: jQuery(".zen-search__refine-header"),
    _mobNavMenus: jQuery(".js-zen-mobile-nav-toggle, .zen-overlay"),
    _megaDropdown: jQuery(".js-mega-dropdown, .js-zen-menu--mega-tab"),
    _slideGallery: jQuery(".zen-slider .sppb-gallery, .js-zen-slick-gallery"),
    _setPrevArrow: "<button class=\"slick-prev slick-arrow\"><i class=\"zen-icon zen-icon--text-lg fontello icon-chevron-left\"></i></button>",
    _setNextArrow: "<button class=\"slick-next slick-arrow\"><i class=\"zen-icon zen-icon--text-lg fontello icon-chevron-right\"></i></button>",
    // Standard JS selectors.
    //
    _bsCarouselElm: document.querySelectorAll(".js-zen-carousel-gallery"),
    _bsTabElm: document.querySelectorAll(".zen-tab__link"),
    _modalObj: document.querySelectorAll(".modal"),
    _setModal: document.getElementById("modals")
	};

  // Set Passive options.
  //
  try {
    var opts = {
      get passive() {
        // This function will be called when the browser
        // attempts to access the passive property.
        gloVar.passive = true;
        return false;
      }
    };
    // Placeholder event handler.
    //
    window.addEventListener("evPlaceholder", null, opts);
    window.removeEventListener("evPlaceholder", null, opts);
  }
  catch(err) {
    gloVar.passive = false;
  }

  document.getElementById('offcanvasSearchForm').addEventListener('submit', function(event) {
    event.preventDefault(); // Prevent the form from submitting normally

    const searchInput = document.getElementById('offcanvasSearchInput');
    const searchText = searchInput.value.trim();

    let searchUrl = '/holidays'; // Default URL if no search text
    if (searchText) {
      searchUrl = `/holidays#/holidays?sort=_score%7CDESC&text=${encodeURIComponent(searchText)}`;
    }
    window.location.href = searchUrl;

    // Close the offcanvas after submitting the form
    const offcanvasElement = document.getElementById('searchOffcanvas');
    const offcanvas = bootstrap.Offcanvas.getInstance(offcanvasElement);
    offcanvas.hide();
  });

  // Initialize popovers
  var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'))
  var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
    // Check if this is an info icon popover
    if (popoverTriggerEl.classList.contains('info-icon')) {
      var popover = new bootstrap.Popover(popoverTriggerEl, {
        html: true,
        trigger: 'manual',
        placement: 'right',
        fallbackPlacements: ['top', 'left', 'bottom'],
        container: 'body',
        sanitize: false,
        allowList: {
          // Allow common HTML tags for rich content
          'strong': [],
          'b': [],
          'em': [],
          'i': [],
          'br': [],
          'p': [],
          'a': ['href', 'title', 'target'],
          'div': ['class'],
          'span': ['class']
        },
        delay: { show: 300, hide: 500 }
      });

      // Add event listener to execute email decloaking scripts when popover is shown
      popoverTriggerEl.addEventListener('shown.bs.popover', function() {
        var popoverElement = document.querySelector('.popover');
        if (popoverElement) {
          // Find all script tags in the popover and execute them
          var scripts = popoverElement.querySelectorAll('script');
          scripts.forEach(function(script) {
            if (script.innerHTML) {
              eval(script.innerHTML);
            }
          });

          // Keep popover open when hovering over it
          popoverElement.addEventListener('mouseenter', function() {
            clearTimeout(popoverTriggerEl._hideTimeout);
          });

          popoverElement.addEventListener('mouseleave', function() {
            popoverTriggerEl._hideTimeout = setTimeout(function() {
              popover.hide();
            }, 100);
          });
        }
      });

      // Handle trigger element mouse events
      popoverTriggerEl.addEventListener('mouseenter', function() {
        clearTimeout(popoverTriggerEl._hideTimeout);
        clearTimeout(popoverTriggerEl._showTimeout);
        popoverTriggerEl._showTimeout = setTimeout(function() {
          popover.show();
        }, 300);
      });

      popoverTriggerEl.addEventListener('mouseleave', function() {
        clearTimeout(popoverTriggerEl._showTimeout);
        popoverTriggerEl._hideTimeout = setTimeout(function() {
          popover.hide();
        }, 500);
      });



      return popover;
    } else {
      // Default popover configuration for other elements
      return new bootstrap.Popover(popoverTriggerEl, {
        html: true,
        trigger: 'click',
        container: 'body',
        content: function() {
          return '<div class="popover-content" onclick="event.stopPropagation()">' +
                  '<h3>Custom HTML Content</h3>' +
                  '<p>This is a paragraph inside the popover.</p>' +
                  '<button class="btn btn-secondary js-popover-action">A button</button>' +
                '</div>';
        }
      });
    }
  });

  // Handle all clicks within popovers (for non-info icon popovers)
  jQuery(document).on('click', '.popover', function(e) {
    e.stopPropagation();
    if (jQuery(e.target).hasClass('js-popover-action')) {
      e.preventDefault();
      // Handle button click here
      console.log('Popover button clicked');
    }
  });

  // Prevent info icon popovers from closing when hovering over the popover itself
  jQuery(document).on('mouseenter', '.popover', function() {
    var popoverId = jQuery(this).attr('id');
    var triggerElement = jQuery('[aria-describedby="' + popoverId + '"]');
    if (triggerElement.hasClass('info-icon')) {
      // Keep the popover open while hovering over it
      clearTimeout(triggerElement.data('popover-timeout'));
    }
  });

  jQuery(document).on('mouseleave', '.popover', function() {
    var popoverId = jQuery(this).attr('id');
    var triggerElement = jQuery('[aria-describedby="' + popoverId + '"]');
    if (triggerElement.hasClass('info-icon')) {
      // Set a timeout to hide the popover after leaving
      var timeout = setTimeout(function() {
        var popoverInstance = bootstrap.Popover.getInstance(triggerElement[0]);
        if (popoverInstance) {
          popoverInstance.hide();
        }
      }, 100);
      triggerElement.data('popover-timeout', timeout);
    }
  });

  // Initialize modals
  var modalTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="modal"]'));
  var modalList = modalTriggerList.map(function (modalTriggerEl) {
    var target = modalTriggerEl.getAttribute('data-bs-target');
    if (target) {
      var modalEl = document.querySelector(target);
      if (modalEl) {
        return new bootstrap.Modal(modalEl, {
          backdrop: 'static',
          keyboard: false
        });
      }
    }
  });

  // Initialize carousels
  var carouselTriggerList = [].slice.call(document.querySelectorAll('.carousel'));
  var carouselList = carouselTriggerList.map(function (carouselEl) {
    return new bootstrap.Carousel(carouselEl, {
      interval: false
    });
  });

  // Handle gallery button clicks
  document.addEventListener('click', function(e) {
    if (e.target.closest('.gallery-button')) {
      var target = e.target.closest('.gallery-button').getAttribute('data-bs-target');
      if (target) {
        var modalEl = document.querySelector(target);
        if (modalEl) {
          var modal = bootstrap.Modal.getInstance(modalEl) || new bootstrap.Modal(modalEl);
          modal.show();
        }
      }
    }
  });

  // Preload script.
  //
  function preloadScript(file, delay) {
    var _scriptEl = document.createElement("script");
    _scriptEl.src = file;
    setTimeout(function () {
      document.body.appendChild(_scriptEl);
      setTimeout(function () {
        renderPreloadContent();
      }, 250);
    }, delay);
  }

  function renderPreloadContent() {
    jQuery.Zen.zenExample();
  }

  // Cookie management functions.
  // Remember to change the Domain name.
  //
  function setCookie(cname, cvalue, exdays) {
    var d = new Date();
    d.setTime(d.getTime() + (exdays * 24 * 60 * 60 * 1000));
    var expires = "expires="+d.toUTCString();
    document.cookie = cname + "=" + cvalue + ";" + expires + ";domain=" + window.location.hostname + ";path=/";
  }

  function getCookie(cname) {
    var name = cname + "=";
    var ca = document.cookie.split(";");
    for(var i = 0; i < ca.length; i++) {
      var c = ca[i];
      while (c.charAt(0) == " ") {
        c = c.substring(1);
      }
      if (c.indexOf(name) == 0) {
        return c.substring(name.length, c.length);
      }
    }
    return "";
  }

  function checkCookie(clabel) {
    var policy = getCookie(clabel);
    if (policy !== "") {
      gloVar._ckPolicy.removeClass(gloVar.showPanel);
    }
    else {
      gloVar._ckPolicy.addClass(gloVar.showPanel);
    }
  }

  // Used for carousels that need lazy loaded images.
  // This ensures the height remains in place while
  // the content renders.
  //
  function galleryHeight() {
    jQuery(gloVar.imgGallry).each(function(_this) {
      _this = jQuery(this);
      var actSlide = _this.find(".active .img-fluid"),
          slideHgt = actSlide.height();
      if (slideHgt !== 0) {
        _this.find(".carousel-inner").css("min-height", slideHgt);
      }
      else {
        _this.find(".carousel-inner").css("min-height", "30vh");
      }
    });
  }

  // Use to remove all data attributes.
  // This includes classes & IDs.
  //
  jQuery.fn.removeAttributes = function() {
    return this.each(function() {
      var attributes = jQuery.map(this.attributes, function(item) {
        return item.name;
      });
      var itm = jQuery(this);
      jQuery.each(attributes, function(i, item) {
        itm.removeAttr(item);
      });
    });
  };

  jQuery.Zen = {
    zenExample: function() {
      console.log("Preload");
    }
  };

  // UGC read more.
  //
  gloVar._readMore.on("click", function(e, _this) {
    _this = jQuery(this);
    var _reviewCard = _this.closest(gloVar.crdReview);
    if (_reviewCard.hasClass(gloVar.actReview)) {
      _reviewCard.removeClass(gloVar.actReview);
    }
    else {
      _reviewCard.addClass(gloVar.actReview);
    }
  });
  // Fix JCH picture element issue.
  //
  var _picture = gloVar._htmlBody.find("picture"),
      _source = _picture.find("source");
  _source.each(function(_this) {
    _this = jQuery(this);
      var srcAttr = _this.attr("srcset"),
          dataScr = _this.data("srcset");
      if (srcAttr) {
        _this.attr("srcset", dataScr);
        _this.removeAttr("data-srcset");
      }
  });

  // Mega Tab toggle.
  //
  jQuery(gloVar._megaButton).on("click", function (e, _this) {
    e.preventDefault();
    _this = jQuery(this);
    _this.next().toggleClass("show");
    if (_this.attr("aria-expanded") === "true") {
      _this.attr("aria-expanded", "false");
    }
    else {
      _this.attr("aria-expanded", "true");
    }
    gloVar._megaButton.not(this).next().removeClass("show");
    gloVar._megaButton.not(this).attr("aria-expanded", "false");
  });

  // Open first menu tab.
  //
  gloVar._megaTabCon.each(function(_this) {
    _this = jQuery(this);
    _this.find(".parent:first .zen-menu__mega-tab-content").addClass("active");
    _this.find(".parent:first " + gloVar.megaTabPill).attr("aria-selected", "true");
  });

  jQuery(gloVar.megaTabPill).on("click", function (e, _this) {
    _this = jQuery(this);
    jQuery(gloVar.megaTabPill).not(this).attr("aria-selected", "false");
    jQuery(gloVar.megaTabPill).not(this).next().removeClass("active");
    _this.attr("aria-selected", "true");
    _this.next().addClass("active");
  });

  // Filter panel customisations.
  //
  jQuery(".js-root-" + gloVar.filterPan).on("click", function() {
    gloVar._refineHeadr.show();
    gloVar._refinePanel.hide();
  });

  gloVar._htmlBody.on("click", ".js-sub-" + gloVar.filterPan, function() {
    gloVar._refineHeadr.hide();
    gloVar._refinePanel.show();
  });

  // Override the user agent select styles.
  //
  gloVar._styleSel.styledSelects();

  // Slick sliders.
  //
  gloVar._slideGallery.slick({
    lazyLoad: 'ondemand',
    dots: false,
    infinite: true,
    speed: 500,
    slidesToShow: 3,
    slidesToScroll: 1,
    draggable: false,
    prevArrow: gloVar._setPrevArrow,
    nextArrow: gloVar._setNextArrow,
    responsive: [{
        breakpoint: 1150,
        settings: {
          slidesToShow: 2
        }
      },
      {
        breakpoint: 991,
        settings: {
          slidesToShow: 2,
          centerMode: true
        }
      },
      {
        breakpoint: 767,
        settings: {
          slidesToShow: 1,
          centerMode: true
        }
      }
    ]
  });

  // Bootstrap 5 tab scroll.
  //
  function tabScroll(_target) {
    var _targetID = document.getElementById(_target),
        _targetScroll = _targetID.offsetTop - 150;
    // Standard offset scroll.
    //
    window.scrollTo({
      top: _targetScroll,
      behavior: "smooth"
    });
  }
  // Find each tab link.
  //
  Array.prototype.forEach.call(gloVar._bsTabElm, function(_tab, index) {
    var scrollTarget = _tab.dataset.scroll_target;
    _tab.addEventListener("click", function(element) {
      // jQuery on event alternative.
      //
      addEvent(_tab, "shown.bs.tab", tabScroll(scrollTarget));
    });
  });

  // Animate Bootstrap 5 carousel image heights.
  //
  function carouselHeight(ev) {
    var _target = ev.relatedTarget,
        _targetHeight = _target.offsetHeight,
        _inner = _target.parentNode;
    if (_targetHeight !== 0) {
      _inner.style.minHeight = _targetHeight + "px";
    }
    else {
      _inner.style.minHeight = "30vh";
    }
  }

  Array.prototype.forEach.call(gloVar._bsCarouselElm, function(_carousel, index) {
    // jQuery on event alternative.
    //
    addEvent(_carousel, "slid.bs.carousel", carouselHeight);
    addEvent(_carousel, "slide.bs.carousel", carouselHeight);
  });

  // Scroll to tab/accordion and open.
  //
  gloVar._scrollActv.on("click", function(_this) {
    _this = jQuery(this);
    var winWidth = window.innerWidth,
        altActTarget = _this.data("scroll-activate"),
        scrollTarget = _this.data("scroll-target"),
        minusTop = _this.data("scroll-minus-top"),
        scrType = _this.data("scroll-type"),
        speed = _this.data("scroll-speed"),
        trimTarget;
    if (scrType === "tab") {
      trimTarget = scrollTarget.slice(0, scrollTarget.length - 4);
    }
    else {
      trimTarget = scrollTarget;
    }
    if (winWidth > 991) {
      if (altActTarget) {
        jQuery("#" + altActTarget).trigger("click");
      }
      else {
        jQuery("#" + scrollTarget).trigger("click");
      }
      setTimeout(function () {
        jQuery("html, body").animate({
          scrollTop: jQuery("#" + trimTarget).offset().top - minusTop
        }, speed);
      }, 200);
    }
    else {
       if (altActTarget) {
        jQuery(gloVar.mobileTab + "[data-bs-target=\"#" + altActTarget + "\"]").next().collapse("show");
      }
      else {
        jQuery(gloVar.mobileTab + "[data-bs-target=\"#" + trimTarget + "\"]").next().collapse("show");
      }
      setTimeout(function () {
        jQuery("html, body").animate({
          scrollTop: jQuery(gloVar.mobileTab + "[data-bs-target=\"#" + trimTarget + "\"]").offset().top - minusTop
        }, speed);
      }, 200);
    }
  });

  // Mobile Menu.
  //
  gloVar._mobNavMenus.on("click", function (e, _this) {
    _this = jQuery(this);
    e.preventDefault();
    var menuType = _this.data("menu-type"),
        selector = _this.data("selector");
    gloVar._htmlBody.toggleClass(menuType);
    jQuery(selector).toggleClass("mobile");
  });

  // Additional overlay hide.
  //
  gloVar._ovlayClk.on("click", function () {
    jQuery(".zen-overlay").trigger("click");
  });

  // Focus parent input field of inline icon.
  //
  gloVar._inputFocus.on("click", function(_this) {
    _this = jQuery(this);
    _this.parent().find("input").trigger("focus");
  });

  // Policy click management.
  //
  gloVar._ckButton.on("click", function() {
    var policy = "checked";
    setCookie("policy-confirm", policy, 30);
    gloVar._ckPolicy.removeClass(gloVar.showPanel);
  });

  // Main on body click functionality.
  //
  gloVar._htmlBody.on("mouseup touchstart", function (e) {
    var _container = jQuery(gloVar._megaDropdown);
    if (!_container.has(e.target).length) {
      _container.removeClass("show");
      gloVar._megaButton.attr("aria-expanded", "false");
    }
  });

  // Main scroll event.
  //
  window.addEventListener("scroll", function() {
    var winWidth = window.innerWidth,
        menuMarker = gloVar._menuMark.offset().top,
        wScrollTop = jQuery(window).scrollTop();
    // Enter the header height in the if statement below.
    // Check no other items effect the height on resize.
    //
    if (winWidth > 991) {
      /*
      if (wScrollTop >= menuMarker) {
        gloVar._fixdHead.addClass(gloVar.fixedMenu);
        gloVar._mainBody.addClass(gloVar.scrlTrans);
      }
      if (wScrollTop < menuMarker) {
        gloVar._fixdHead.removeClass(gloVar.fixedMenu);
        gloVar._mainBody.removeClass(gloVar.scrlTrans);
      }
      */
    }
    else {
      gloVar._fixdHead.removeClass(gloVar.fixedMenu);
      gloVar._mainBody.removeClass(gloVar.scrlTrans);
      if (wScrollTop > 300) {
        gloVar._bckToTop.addClass(gloVar.showBtn);
      }
      else {
        gloVar._bckToTop.removeClass(gloVar.showBtn);
      }
    }
  }, gloVar.passive ? { passive: true } : false);

  // On resize.
  //
  jQuery(window).on("load resize", function () {
    var winWidth = window.innerWidth;
    document.cookie = "winWidth=" + winWidth + ";domain=" + window.location.hostname + ";path=/";
    // Bootstrap carousel & lazy load support.
    // Update on resize.
    //
    galleryHeight();
    // Mobile and Desktop statements.
    //
    if (winWidth > 991) {
      gloVar._moblTabs.each(function(_this) {
        _this = jQuery(this);
        var setID = _this.attr("aria-controls");
        if (_this.next().hasClass("collapse") && _this.parent().hasClass("tab-content")) {
          _this.next().removeAttributes().addClass("tab-pane fade").attr({
            id: setID,
            role: "tabpanel",
            "aria-labelledby": setID + "-tab"
          });
          _this.next().find(":first").removeClass("mt-4");
          jQuery(".zen-tab__container a").each(function(_this) {
            _this = jQuery(this);
            _this.tab();
          });
          jQuery(".zen-tab__link").removeClass("active");
          jQuery(gloVar.dfaultTab).trigger("click").addClass("active");
          jQuery("#trip-overview-tab-content").addClass("active show");
        }
      });
    }
    else {
      gloVar._moblTabs.each(function(_this) {
        _this = jQuery(this);
        var setID = _this.attr("aria-controls");
        if (!_this.next().hasClass("collapse")) {
          _this.next().removeAttributes().addClass("collapse").attr("id", setID);
        }
        if (_this.attr("aria-expanded") === "true") {
          _this.next().collapse("show");
        }
        else {
          _this.next().collapse("hide");
        }
      });
    }
  });

  // Back to top feature.
  //
  gloVar._bckToTop.on("click", function(e) {
    e.preventDefault();
    jQuery("html, body").animate({
      scrollTop: 0
    }, 300);
  });

  (function () {
    // Bootstrap carousel & lazy load support.
    // Update on resize.
    //
    galleryHeight();
    // Check policy cookie.
    //
    checkCookie("policy-confirm");

    // Avoid render blocking.
    //
    setTimeout(function () {
      gloVar._ckPolicy.show();
    }, 5000);

    // Move modals.
    //
    Array.prototype.forEach.call(gloVar._modalObj, function(element, index) {
      var _getParent = element.parentNode;
      _getParent.removeChild(element);
      if (!hasClass(_getParent, "modals") && !hasClass(element, "dont-move")) {
        gloVar._setModal.appendChild(element);
      }
    });
  })();

  // Mobile tab toggle for dates & prices
  jQuery('.js-dates-prices-tab').on('click', function(e) {
    e.preventDefault();
    e.stopPropagation();

    var $button = jQuery(this);
    var $content = jQuery($button.data('bs-target'));
    var isExpanded = $button.attr('aria-expanded') === 'true';

    if (isExpanded) {
      $content.collapse('hide');
      $button.attr('aria-expanded', 'false');
    } else {
      $content.collapse('show');
      $button.attr('aria-expanded', 'true');
    }
  });

  // --- BEGIN: Holiday Tab Navigation (Delegated to deep-linking.js) ---
  jQuery(function() {
    // Tab navigation is now handled by deep-linking.js
    console.log('Tab navigation is now handled by deep-linking.js');

    // No need to duplicate the tab navigation logic here
    // All functionality has been moved to deep-linking.js
  });
  // --- END: Holiday Tab Navigation (Delegated to deep-linking.js) ---
});

/*==============================================================================
// jQuery replacements and alternatives
//============================================================================*/

  var hasClass = function (el, cl) {
    var regex = new RegExp("(?:\\s|^)" + cl + "(?:\\s|$)");
    return !!el.className.match(regex);
  },
  addClass = function (el, cl) {
    el.className += " " + cl;
  },
  removeClass = function (el, cl) {
    var regex = new RegExp("(?:\\s|^)" + cl + "(?:\\s|$)");
    el.className = el.className.replace(regex, " ");
  },
  toggleClass = function (el, cl) {
    return hasClass(el, cl) ? removeClass(el, cl) : addClass(el, cl);
  };

  // Example:
  // addClass(myExample, "radiant-example");

/*------------------------------------------------------------------------------
// Replace jQuery wrap();
//----------------------------------------------------------------------------*/

  function wrap(el, wrapper) {
    el.parentNode.insertBefore(wrapper, el);
     wrapper.appendChild(el);
  }

  // Example:
  // wrap(example, document.createElement("div"));

/*------------------------------------------------------------------------------
// Replace jQuery after();
//----------------------------------------------------------------------------*/

  function insertAfter(referenceNode, newNode) {
    referenceNode.parentNode.insertBefore(newNode, referenceNode.nextSibling);
  }

  // Example:
  // insertAfter(exampleTarget, exampleValue);

/*------------------------------------------------------------------------------
// Replace jQuery each();
//----------------------------------------------------------------------------*/

  var example = document.querySelectorAll(".example");

  Array.prototype.forEach.call(example, function(elements, index) {
    // add condition
  });

/*------------------------------------------------------------------------------
// Adding event & function
//----------------------------------------------------------------------------*/

  var addEvent = (function () {
    var filter = function(el, type, fn) {
      for ( var i = 0, len = el.length; i < len; i++ ) {
        addEvent(el[i], type, fn);
      }
    };
    if ( document.addEventListener ) {
      return function (el, type, fn) {
        if ( el && el.nodeName || el === window ) {
          el.addEventListener(type, fn, false);
        }
        else if (el && el.length) {
          filter(el, type, fn);
        }
      };
    }
    return function (el, type, fn) {
      if ( el && el.nodeName || el === window ) {
        el.attachEvent("on" + type, function () { return fn.call(el, window.event); });
      }
      else if ( el && el.length ) {
        filter(el, type, fn);
      }
    };
  })();

  // Example:
  // addEvent( document.getElementsByTagName("a"), "click", exampleFunction);
