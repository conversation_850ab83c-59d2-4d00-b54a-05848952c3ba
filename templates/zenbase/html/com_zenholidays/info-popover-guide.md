# Info Popover Placeholder System

## Overview
The info popover system allows you to add information icons with hover tooltips/popovers directly within copy items. When users hover over the icon, they see additional information in a styled popover.

## Syntax
Use the following syntax in any copy item content:

```
{{info:unique-id|title|content}}
```

### Parameters:
- **unique-id**: A unique identifier for the placeholder (required)
- **title**: Optional title that appears at the top of the popover (can be empty)
- **content**: HTML content for the popover body (required)

### Delimiter:
Use the pipe character `|` to separate the three parts.

## Examples

### Basic Example (no title)
```
{{info:internal-flights||We'll introduce you to our trusted in-country supplier, who can arrange these flights based on your trip dates. The cost is typically around $450 per person.}}
```

### With Title
```
{{info:visa-requirements|Visa Information|Visa requirements vary by nationality. Please check with your local embassy for the most up-to-date information.}}
```

### With HTML Content
```
{{info:weather|Weather Conditions|<strong>Dry Season:</strong> May to October<br><strong>Wet Season:</strong> November to April<br><em>Pack accordingly!</em>}}
```

### With Links and Rich Formatting
```
{{info:contact|Contact Details|Email us at <a href="mailto:<EMAIL>"><EMAIL></a> or call <strong>+44 1234 567890</strong>}}
```

### Multiple Placeholders
You can use multiple placeholders in the same copy item:
```
This trip includes accommodation and meals {{info:meals|Meal Information|All meals are included except lunch on day 3}} but excludes flights {{info:flights||Internal flights cost approximately $450 per person}}.
```

## HTML Support
The content section fully supports HTML tags including:
- `<strong>`, `<b>` for **bold text**
- `<em>`, `<i>` for *italic text*
- `<br>` for line breaks
- `<p>` for paragraphs
- `<a href="...">` for clickable links
- `<div>`, `<span>` with class attributes for custom styling

**Important**: HTML tags will be rendered as actual formatting, not displayed as code. Use this to create rich, interactive popover content.

## Best Practices

### Unique IDs
- Use descriptive, kebab-case IDs: `internal-flights`, `visa-requirements`, `weather-conditions`
- Keep IDs consistent across similar content types
- Avoid spaces and special characters in IDs

### Content Guidelines
- Keep popover content concise (2-3 sentences max)
- Use HTML formatting sparingly for better readability
- Test content on mobile devices to ensure it displays well

### Title Usage
- Use titles for complex topics that benefit from a header
- Leave title empty (but keep the `|` delimiter) for simple explanations
- Keep titles short (1-5 words)

## Technical Details

### CSS Classes
The system generates HTML with these classes:
- `.info-icon` - The information icon
- `.popover-content` - Container for popover content

### JavaScript Behavior
- Popovers trigger on hover and focus
- 200ms delay on show, 100ms delay on hide
- Popovers stay open when hovering over the popover itself
- Automatic positioning (top placement preferred)

### Icon
Uses Material Icons "info" icon, styled with the site's primary color (#FE7720).

## Troubleshooting

### Popover Not Showing
- Check that all three parts are present (even if title is empty)
- Ensure proper pipe `|` delimiters
- Verify no extra spaces around the placeholder

### Content Not Formatting
- Check HTML syntax in the content section
- Ensure quotes are properly escaped
- Test with simple text first, then add HTML

### Multiple Popovers Conflicting
- Ensure each placeholder has a unique ID
- Check for duplicate IDs across different copy items

## Examples in Context

### Accommodation Copy Item
```
Stay in comfortable mountain lodges throughout your trek {{info:accommodation|Lodge Standards|All lodges are twin-share with basic amenities. Single supplements available for £150}}. Meals are provided {{info:meals||Breakfast and dinner included daily. Lunch is provided on trekking days only}}.
```

### Itinerary Copy Item
```
Day 3: Summit attempt {{info:summit-day|Summit Day Details|Start at 2am for sunrise summit. Weather dependent - alternative routes available if conditions are poor}}. Return to base camp for celebration dinner.
```
