<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HTML Rendering Demo - Info Popovers</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Outlined" rel="stylesheet">
    
    <!-- Custom CSS -->
    <style>
        .info-icon {
            font-size: 16px;
            color: #FE7720;
            cursor: pointer;
            margin-left: 4px;
            vertical-align: middle;
            transition: color 0.2s ease;
        }

        .info-icon:hover {
            color: #FFB347;
        }

        .container {
            margin-top: 50px;
        }
        
        .demo-section {
            margin-bottom: 40px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        
        .good { border-color: #28a745; background-color: #f8fff9; }
        .bad { border-color: #dc3545; background-color: #fff8f8; }
        
        .popover .popover-body {
            padding: 12px;
            max-width: 300px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>HTML Rendering in Info Popovers</h1>
        <p class="lead">This demo shows how HTML content is properly rendered in popovers, not displayed as code.</p>
        
        <div class="demo-section good">
            <h3>✅ Correct: HTML Rendered as Formatting</h3>
            <p>Contact our team <i class="info-icon material-icons-outlined" 
               data-bs-toggle="popover" 
               data-bs-trigger="hover focus" 
               data-bs-placement="top" 
               data-bs-html="true" 
               data-bs-title="Contact Information" 
               data-bs-content="<div class='popover-content'>Email: <a href='mailto:<EMAIL>'><EMAIL></a><br><strong>Phone:</strong> +44 1234 567890<br><em>Available 9am-5pm GMT</em></div>" 
               data-info-id="contact-good">info</i> for assistance.</p>
            <small class="text-muted">Hover over the info icon to see properly formatted HTML content</small>
        </div>
        
        <div class="demo-section good">
            <h3>✅ Rich Content Example</h3>
            <p>Weather information <i class="info-icon material-icons-outlined" 
               data-bs-toggle="popover" 
               data-bs-trigger="hover focus" 
               data-bs-placement="top" 
               data-bs-html="true" 
               data-bs-title="Seasonal Weather Guide" 
               data-bs-content="<div class='popover-content'><strong>Dry Season (May-Oct):</strong><br>• Perfect trekking conditions<br>• Clear mountain views<br>• Temperatures: 15-25°C<br><br><strong>Wet Season (Nov-Apr):</strong><br>• Occasional afternoon showers<br>• Lush green landscapes<br>• Temperatures: 10-20°C<br><br><em>Pack layers for all conditions!</em></div>" 
               data-info-id="weather-rich">info</i> varies by season.</p>
            <small class="text-muted">Complex formatting with bullet points, temperature ranges, and styling</small>
        </div>
        
        <div class="demo-section good">
            <h3>✅ Interactive Links</h3>
            <p>Additional resources <i class="info-icon material-icons-outlined" 
               data-bs-toggle="popover" 
               data-bs-trigger="hover focus" 
               data-bs-placement="top" 
               data-bs-html="true" 
               data-bs-title="Helpful Resources" 
               data-bs-content="<div class='popover-content'>• <a href='https://www.gov.uk/foreign-travel-advice' target='_blank'>UK Travel Advice</a><br>• <a href='mailto:<EMAIL>'>Email our team</a><br>• <strong>Emergency:</strong> +44 24/7 hotline<br><br><em>Links open in new tabs</em></div>" 
               data-info-id="resources">info</i> are available.</p>
            <small class="text-muted">Clickable links that work within the popover</small>
        </div>
        
        <div class="demo-section">
            <h3>📝 Placeholder Syntax Examples</h3>
            <p><strong>Simple text:</strong></p>
            <code>{{info:basic||This is basic information without HTML}}</code>
            
            <p><strong>With bold and italic:</strong></p>
            <code>{{info:formatted|Title|&lt;strong&gt;Important:&lt;/strong&gt; This is &lt;em&gt;emphasized&lt;/em&gt; text}}</code>
            
            <p><strong>With links:</strong></p>
            <code>{{info:links|Resources|Visit &lt;a href="https://example.com"&gt;our website&lt;/a&gt; for more info}}</code>
            
            <p><strong>Complex formatting:</strong></p>
            <code>{{info:complex|Details|&lt;strong&gt;Season:&lt;/strong&gt; May-Oct&lt;br&gt;&lt;em&gt;Perfect conditions!&lt;/em&gt;}}</code>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.6/dist/umd/popper.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <script>
        // Initialize popovers with HTML support
        var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'))
        var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
            if (popoverTriggerEl.classList.contains('info-icon')) {
                return new bootstrap.Popover(popoverTriggerEl, {
                    html: true,
                    trigger: 'hover focus',
                    placement: 'top',
                    container: 'body',
                    sanitize: false,
                    allowList: {
                        'strong': [],
                        'b': [],
                        'em': [],
                        'i': [],
                        'br': [],
                        'p': [],
                        'a': ['href', 'title', 'target'],
                        'div': ['class'],
                        'span': ['class']
                    },
                    delay: { show: 200, hide: 100 }
                });
            }
        });

        // Enhanced hover behavior
        jQuery(document).on('mouseenter', '.popover', function() {
            var popoverId = jQuery(this).attr('id');
            var triggerElement = jQuery('[aria-describedby="' + popoverId + '"]');
            if (triggerElement.hasClass('info-icon')) {
                clearTimeout(triggerElement.data('popover-timeout'));
            }
        });

        jQuery(document).on('mouseleave', '.popover', function() {
            var popoverId = jQuery(this).attr('id');
            var triggerElement = jQuery('[aria-describedby="' + popoverId + '"]');
            if (triggerElement.hasClass('info-icon')) {
                var timeout = setTimeout(function() {
                    var popoverInstance = bootstrap.Popover.getInstance(triggerElement[0]);
                    if (popoverInstance) {
                        popoverInstance.hide();
                    }
                }, 100);
                triggerElement.data('popover-timeout', timeout);
            }
        });
    </script>
</body>
</html>
