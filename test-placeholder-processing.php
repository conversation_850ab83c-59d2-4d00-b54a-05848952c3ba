<?php
/**
 * Test script for Info Popover Placeholder Processing
 * 
 * This script tests the regex pattern and replacement logic
 * used in the holiday content plugin.
 */

// Test the processInfoPopover function logic
function processInfoPopover($matches) {
    $id = trim($matches[1]);
    $title = trim($matches[2]);
    $content = trim($matches[3]);
    
    // Escape attributes for HTML safety
    $escapedTitle = htmlspecialchars($title, ENT_QUOTES, 'UTF-8');
    
    // Build the popover HTML
    $html = '<i class="info-icon material-icons-outlined" ';
    $html .= 'data-bs-toggle="popover" ';
    $html .= 'data-bs-trigger="hover focus" ';
    $html .= 'data-bs-placement="top" ';
    $html .= 'data-bs-html="true" ';
    
    if (!empty($title)) {
        $html .= 'data-bs-title="' . $escapedTitle . '" ';
    }
    
    // For content, we need to allow HTML but escape quotes
    $popoverContent = str_replace('"', '&quot;', $content);
    $html .= 'data-bs-content="<div class=\'popover-content\'>' . $popoverContent . '</div>" ';
    $html .= 'data-info-id="' . htmlspecialchars($id, ENT_QUOTES, 'UTF-8') . '">';
    $html .= 'info</i>';
    
    return $html;
}

// Test cases
$testCases = [
    // Test 1: Basic placeholder without title
    [
        'input' => 'Internal flights are not included {{info:internal-flights||We\'ll introduce you to our trusted in-country supplier, who can arrange these flights based on your trip dates.}} in the price.',
        'description' => 'Basic placeholder without title'
    ],
    
    // Test 2: Placeholder with title
    [
        'input' => 'Check visa requirements {{info:visa-requirements|Visa Information|Visa requirements vary by nationality. Please check with your local embassy.}} before traveling.',
        'description' => 'Placeholder with title'
    ],
    
    // Test 3: Placeholder with HTML content
    [
        'input' => 'Weather varies {{info:weather|Weather Conditions|<strong>Dry Season:</strong> May to October<br><strong>Wet Season:</strong> November to April}} throughout the year.',
        'description' => 'Placeholder with HTML content'
    ],

    // Test 3b: Placeholder with links and more HTML
    [
        'input' => 'For more information {{info:contact|Contact Details|Email us at <a href="mailto:<EMAIL>"><EMAIL></a> or call <strong>+44 1234 567890</strong>}} anytime.',
        'description' => 'Placeholder with links and bold text'
    ],
    
    // Test 4: Multiple placeholders
    [
        'input' => 'This trip includes accommodation {{info:accommodation|Lodge Standards|All lodges are twin-share with basic amenities}} and meals {{info:meals||Breakfast and dinner included daily}} but excludes flights {{info:flights||Internal flights cost approximately $450 per person}}.',
        'description' => 'Multiple placeholders in one text'
    ],
    
    // Test 5: Edge case - empty content (should not match)
    [
        'input' => 'This should not match {{info:test||}} because content is empty.',
        'description' => 'Edge case - empty content'
    ],
    
    // Test 6: Edge case - malformed placeholder
    [
        'input' => 'This should not match {{info:test|title}} because it\'s missing content.',
        'description' => 'Edge case - malformed placeholder'
    ]
];

echo "<h1>Info Popover Placeholder Processing Test</h1>\n\n";

foreach ($testCases as $index => $testCase) {
    echo "<h2>Test " . ($index + 1) . ": " . $testCase['description'] . "</h2>\n";
    echo "<h3>Input:</h3>\n";
    echo "<pre>" . htmlspecialchars($testCase['input']) . "</pre>\n";
    
    // Process the input using the same regex pattern as the plugin
    $processed = preg_replace_callback(
        '/\{\{info:([^|]+)\|([^|]*)\|([^}]+)\}\}/i',
        'processInfoPopover',
        $testCase['input']
    );
    
    echo "<h3>Output:</h3>\n";
    echo "<pre>" . htmlspecialchars($processed) . "</pre>\n";
    
    echo "<h3>Rendered HTML:</h3>\n";
    echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 10px 0; background: #f9f9f9;'>";
    echo $processed;
    echo "</div>\n";
    
    // Check if any replacements were made
    $replacements = preg_match_all('/\{\{info:([^|]+)\|([^|]*)\|([^}]+)\}\}/i', $testCase['input']);
    echo "<p><strong>Placeholders found:</strong> " . $replacements . "</p>\n";
    
    echo "<hr>\n\n";
}

// Test the regex pattern itself
echo "<h2>Regex Pattern Analysis</h2>\n";
echo "<p><strong>Pattern:</strong> <code>/\\{\\{info:([^|]+)\\|([^|]*)\\|([^}]+)\\}\\}/i</code></p>\n";
echo "<p><strong>Explanation:</strong></p>\n";
echo "<ul>\n";
echo "<li><code>\\{\\{info:</code> - Matches literal '{{info:'</li>\n";
echo "<li><code>([^|]+)</code> - Captures ID (one or more non-pipe characters)</li>\n";
echo "<li><code>\\|</code> - Matches literal pipe delimiter</li>\n";
echo "<li><code>([^|]*)</code> - Captures title (zero or more non-pipe characters)</li>\n";
echo "<li><code>\\|</code> - Matches literal pipe delimiter</li>\n";
echo "<li><code>([^}]+)</code> - Captures content (one or more non-brace characters)</li>\n";
echo "<li><code>\\}\\}</code> - Matches literal '}}'</li>\n";
echo "<li><code>i</code> - Case insensitive flag</li>\n";
echo "</ul>\n";

?>

<!DOCTYPE html>
<html>
<head>
    <title>Placeholder Processing Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        pre { background: #f0f0f0; padding: 10px; border-radius: 4px; overflow-x: auto; }
        code { background: #f0f0f0; padding: 2px 4px; border-radius: 2px; }
        hr { margin: 30px 0; }
        .info-icon { color: #FE7720; font-weight: bold; cursor: pointer; }
    </style>
</head>
<body>
    <!-- Content will be output by PHP above -->
</body>
</html>
